using DocFlowSystem.Models;

namespace DocFlowSystem.Helpers
{
    public static class SessionManager
    {
        private static User? _currentUser;
        private static DateTime _loginTime;

        public static User? CurrentUser
        {
            get => _currentUser;
            set
            {
                _currentUser = value;
                if (value != null)
                {
                    _loginTime = DateTime.Now;
                }
            }
        }

        public static bool IsLoggedIn => _currentUser != null;

        public static string CurrentUserName => _currentUser?.FullName ?? "غير معروف";

        public static string CurrentUserRole => _currentUser?.UserRole?.RoleName ?? "غير محدد";

        public static int CurrentUserId => _currentUser?.UserId ?? 0;

        public static DateTime LoginTime => _loginTime;

        public static TimeSpan SessionDuration => DateTime.Now - _loginTime;

        public static bool HasPermission(string permission)
        {
            if (!IsLoggedIn) return false;

            return _currentUser?.UserRole?.RoleName switch
            {
                "مدير" => true, // Admin has all permissions
                "مستخدم" => permission != "إدارة_المستخدمين" && permission != "النسخ_الاحتياطي" && permission != "حذف_البيانات",
                "أرشيف" => permission == "البحث" || permission == "عرض_التقارير" || permission == "الأرشفة" || permission == "عرض_البيانات",
                _ => false
            };
        }

        public static bool CanCreateMail() => HasPermission("إنشاء_البريد");
        public static bool CanEditMail() => HasPermission("تعديل_البريد");
        public static bool CanDeleteMail() => HasPermission("حذف_البريد");
        public static bool CanViewReports() => HasPermission("عرض_التقارير");
        public static bool CanManageUsers() => HasPermission("إدارة_المستخدمين");
        public static bool CanBackupData() => HasPermission("النسخ_الاحتياطي");
        public static bool CanArchiveData() => HasPermission("الأرشفة");

        public static void Logout()
        {
            _currentUser = null;
            _loginTime = DateTime.MinValue;
        }

        public static string GetUserDisplayInfo()
        {
            if (!IsLoggedIn) return "غير مسجل";

            return $"{CurrentUserName} ({CurrentUserRole}) - مدة الجلسة: {SessionDuration:hh\\:mm\\:ss}";
        }
    }
}
