﻿using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using DocFlowSystem.Helpers;
using DocFlowSystem.Services.Interfaces;
using DocFlowSystem.Views.Login;
using DocFlowSystem.Views.IncomingMail;
using DocFlowSystem.Views.OutgoingMail;
using DocFlowSystem.Views.Reports;
using Microsoft.Extensions.DependencyInjection;

namespace DocFlowSystem;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private readonly IAuthenticationService _authenticationService;
    private readonly IServiceProvider _serviceProvider;
    private DispatcherTimer _clockTimer = null!;

    public MainWindow(IAuthenticationService authenticationService, IServiceProvider serviceProvider)
    {
        InitializeComponent();
        _authenticationService = authenticationService;
        _serviceProvider = serviceProvider;

        InitializeWindow();
        SetupClock();
        LoadDashboard();
    }

    private void InitializeWindow()
    {
        // Update user info
        var currentUser = SessionManager.CurrentUser;
        if (currentUser != null)
        {
            UserInfoTextBlock.Text = $"مرحباً، {currentUser.FullName} ({currentUser.UserRole?.RoleName})";

            // Show/hide admin-only features
            if (SessionManager.HasPermission("إدارة_المستخدمين"))
            {
                AdminSectionHeader.Visibility = Visibility.Visible;
                UserManagementButton.Visibility = Visibility.Visible;
                SettingsButton.Visibility = Visibility.Visible;
            }
        }

        // StatusTextBlock.Text = "مرحباً بك في نظام إدارة البريد الحكومي"; // Removed status bar
    }

    private void SetupClock()
    {
        _clockTimer = new DispatcherTimer();
        _clockTimer.Interval = TimeSpan.FromSeconds(1);
        _clockTimer.Tick += (s, e) =>
        {
            // DateTimeTextBlock.Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss"); // Removed clock
        };
        _clockTimer.Start();
    }

    private void LoadDashboard()
    {
        // Load dashboard content
        // StatusTextBlock.Text = "تم تحميل لوحة التحكم"; // Removed status bar
        SetActiveButton(DashboardButton);

        // Create a simple dashboard
        var dashboardContent = new Grid();
        dashboardContent.Children.Add(new TextBlock
        {
            Text = "مرحباً بك في لوحة التحكم\n\nاستخدم القائمة الجانبية للتنقل بين الأقسام المختلفة",
            FontSize = 16,
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            TextAlignment = TextAlignment.Center
        });

        MainFrame.Content = dashboardContent;
    }

    private void SetActiveButton(Button activeButton)
    {
        // Reset all buttons to normal style
        DashboardButton.Style = (Style)FindResource("Win11MenuButton");
        IncomingMailListButton.Style = (Style)FindResource("Win11MenuButton");
        AddIncomingMailButton.Style = (Style)FindResource("Win11MenuButton");
        OutgoingMailListButton.Style = (Style)FindResource("Win11MenuButton");
        AddOutgoingMailButton.Style = (Style)FindResource("Win11MenuButton");
        ReportsButton.Style = (Style)FindResource("Win11MenuButton");
        ArchiveButton.Style = (Style)FindResource("Win11MenuButton");
        UserManagementButton.Style = (Style)FindResource("Win11MenuButton");
        SettingsButton.Style = (Style)FindResource("Win11MenuButton");

        // Set active button style
        activeButton.Style = (Style)FindResource("Win11ActiveMenuButton");
    }

    // Event Handlers
    private void DashboardButton_Click(object sender, RoutedEventArgs e)
    {
        LoadDashboard();
    }

    private void IncomingMailListButton_Click(object sender, RoutedEventArgs e)
    {
        // StatusTextBlock.Text = "قائمة البريد الوارد"; // Removed status bar
        SetActiveButton(IncomingMailListButton);

        try
        {
            var incomingMailService = _serviceProvider.GetRequiredService<IIncomingMailService>();
            var incomingMailListView = new IncomingMailListView(incomingMailService, _serviceProvider);
            MainFrame.Content = incomingMailListView;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تحميل قائمة البريد الوارد: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            ShowComingSoon("قائمة البريد الوارد");
        }
    }

    private void AddIncomingMailButton_Click(object sender, RoutedEventArgs e)
    {
        if (!SessionManager.CanCreateMail())
        {
            MessageBox.Show("ليس لديك صلاحية لإضافة بريد وارد", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // StatusTextBlock.Text = "إضافة بريد وارد جديد"; // Removed status bar
        SetActiveButton(AddIncomingMailButton);

        try
        {
            var incomingMailService = _serviceProvider.GetRequiredService<IIncomingMailService>();
            var addIncomingMailView = new AddIncomingMailView(incomingMailService, _serviceProvider);

            // Handle save event to navigate back to list
            addIncomingMailView.IncomingMailSaved += (s, args) => IncomingMailListButton_Click(sender, e);

            MainFrame.Content = addIncomingMailView;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تحميل نافذة إضافة البريد الوارد: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            ShowComingSoon("إضافة بريد وارد");
        }
    }

    private void OutgoingMailListButton_Click(object sender, RoutedEventArgs e)
    {
        // StatusTextBlock.Text = "قائمة البريد الصادر"; // Removed status bar
        SetActiveButton(OutgoingMailListButton);

        try
        {
            var outgoingMailService = _serviceProvider.GetRequiredService<IOutgoingMailService>();
            var outgoingMailListView = new OutgoingMailListView(outgoingMailService, _serviceProvider);
            MainFrame.Content = outgoingMailListView;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تحميل قائمة البريد الصادر: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            ShowComingSoon("قائمة البريد الصادر");
        }
    }

    private void AddOutgoingMailButton_Click(object sender, RoutedEventArgs e)
    {
        if (!SessionManager.CanCreateMail())
        {
            MessageBox.Show("ليس لديك صلاحية لإضافة بريد صادر", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // StatusTextBlock.Text = "إضافة بريد صادر جديد"; // Removed status bar
        SetActiveButton(AddOutgoingMailButton);

        try
        {
            var outgoingMailService = _serviceProvider.GetRequiredService<IOutgoingMailService>();
            var addOutgoingMailView = new AddOutgoingMailView(outgoingMailService, _serviceProvider);

            // Handle save event to navigate back to list
            addOutgoingMailView.OutgoingMailSaved += (s, args) => OutgoingMailListButton_Click(sender, e);

            MainFrame.Content = addOutgoingMailView;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تحميل نافذة إضافة البريد الصادر: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            ShowComingSoon("إضافة بريد صادر");
        }
    }

    private void ReportsButton_Click(object sender, RoutedEventArgs e)
    {
        if (!SessionManager.CanViewReports())
        {
            MessageBox.Show("ليس لديك صلاحية لعرض التقارير", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // StatusTextBlock.Text = "التقارير"; // Removed status bar
        SetActiveButton(ReportsButton);

        try
        {
            var incomingMailService = _serviceProvider.GetRequiredService<IIncomingMailService>();
            var outgoingMailService = _serviceProvider.GetRequiredService<IOutgoingMailService>();
            var reportService = _serviceProvider.GetRequiredService<IReportService>();
            var reportsView = new ReportsView(incomingMailService, outgoingMailService, reportService);
            MainFrame.Content = reportsView;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تحميل التقارير: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            ShowComingSoon("التقارير");
        }
    }

    private void ArchiveButton_Click(object sender, RoutedEventArgs e)
    {
        if (!SessionManager.CanArchiveData())
        {
            MessageBox.Show("ليس لديك صلاحية للوصول إلى الأرشيف", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // StatusTextBlock.Text = "الأرشيف"; // Removed status bar
        SetActiveButton(ArchiveButton);

        // For now, show a simple message about archive functionality
        var archiveMessage = "🗃️ نظام الأرشفة\n\n" +
                           "• أرشفة البريد القديم\n" +
                           "• البحث في السجلات المؤرشفة\n" +
                           "• إدارة مساحة التخزين\n\n" +
                           "هذه الوظيفة قيد التطوير وستكون متاحة قريباً.";

        MessageBox.Show(archiveMessage, "الأرشيف", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void UserManagementButton_Click(object sender, RoutedEventArgs e)
    {
        if (!SessionManager.CanManageUsers())
        {
            MessageBox.Show("ليس لديك صلاحية للوصول إلى إدارة المستخدمين", "تحذير",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // StatusTextBlock.Text = "إدارة المستخدمين"; // Removed status bar
        SetActiveButton(UserManagementButton);

        try
        {
            var userService = _serviceProvider.GetRequiredService<IUserService>();
            var userManagementView = new Views.Users.UserManagementView(userService, _serviceProvider);
            MainFrame.Content = userManagementView;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء فتح إدارة المستخدمين: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void SettingsButton_Click(object sender, RoutedEventArgs e)
    {
        if (!SessionManager.CanManageUsers())
        {
            MessageBox.Show("ليس لديك صلاحية للوصول إلى الإعدادات", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // StatusTextBlock.Text = "الإعدادات"; // Removed status bar
        SetActiveButton(SettingsButton);

        try
        {
            var settingsView = new Views.Settings.SettingsView(_serviceProvider);
            MainFrame.Content = settingsView;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء فتح الإعدادات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            ShowComingSoon("الإعدادات");
        }
    }

    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        this.WindowState = WindowState.Minimized;
    }

    private void MaximizeButton_Click(object sender, RoutedEventArgs e)
    {
        if (this.WindowState == WindowState.Maximized)
        {
            this.WindowState = WindowState.Normal;
            MaximizeButton.Content = "\uE922"; // Maximize icon
        }
        else
        {
            this.WindowState = WindowState.Maximized;
            MaximizeButton.Content = "\uE923"; // Restore icon
        }
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        // Ask for confirmation before closing
        var result = MessageBox.Show("هل تريد إغلاق التطبيق؟", "تأكيد الإغلاق",
            MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            Application.Current.Shutdown();
        }
    }



    private void ShowComingSoon(string feature)
    {
        var content = new Grid();
        content.Children.Add(new TextBlock
        {
            Text = $"قريباً...\n\n{feature}\n\nهذه الميزة قيد التطوير",
            FontSize = 16,
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            TextAlignment = TextAlignment.Center,
            Foreground = Brushes.Gray
        });

        MainFrame.Content = content;
    }

    private void LogoutButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد تسجيل الخروج",
            MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            // Clear session
            SessionManager.Logout();

            // Show login window
            var loginWindow = _serviceProvider.GetRequiredService<LoginWindow>();
            Application.Current.MainWindow = loginWindow;
            loginWindow.Show();

            // Close main window
            this.Close();
        }
    }

    protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
    {
        var result = MessageBox.Show("هل تريد إغلاق التطبيق؟", "تأكيد الإغلاق",
            MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.No)
        {
            e.Cancel = true;
            return;
        }

        // Clear session and shutdown
        SessionManager.Logout();
        Application.Current.Shutdown();

        base.OnClosing(e);
    }

    protected override void OnClosed(EventArgs e)
    {
        _clockTimer?.Stop();
        base.OnClosed(e);
    }
}