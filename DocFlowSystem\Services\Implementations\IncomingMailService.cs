using DocFlowSystem.Data.Repositories;
using DocFlowSystem.Models;
using DocFlowSystem.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace DocFlowSystem.Services.Implementations
{
    public class IncomingMailService : IIncomingMailService
    {
        private readonly IUnitOfWork _unitOfWork;

        public IncomingMailService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<IncomingMail> CreateAsync(IncomingMail incomingMail)
        {
            if (string.IsNullOrEmpty(incomingMail.IncomingNumber))
            {
                incomingMail.IncomingNumber = await GenerateIncomingNumberAsync();
            }

            incomingMail.CreatedDate = DateTime.Now;
            incomingMail.Status = "جديد";

            await _unitOfWork.IncomingMails.AddAsync(incomingMail);
            await _unitOfWork.SaveChangesAsync();

            await LogActivityAsync(incomingMail.CreatedByUserId, "إضافة", "IncomingMails", incomingMail.IncomingMailId);

            return incomingMail;
        }

        public async Task<IncomingMail?> GetByIdAsync(int id)
        {
            // For now, return basic entity - in a real app, you'd include related data
            return await _unitOfWork.IncomingMails.GetByIdAsync(id);
        }

        public async Task<IEnumerable<IncomingMail>> GetAllAsync()
        {
            // For now, return basic entities - in a real app, you'd include related data
            return await _unitOfWork.IncomingMails.GetAllAsync();
        }

        public async Task<IncomingMail> UpdateAsync(IncomingMail incomingMail)
        {
            incomingMail.UpdatedDate = DateTime.Now;
            _unitOfWork.IncomingMails.Update(incomingMail);
            await _unitOfWork.SaveChangesAsync();

            await LogActivityAsync(incomingMail.CreatedByUserId, "تعديل", "IncomingMails", incomingMail.IncomingMailId);

            return incomingMail;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var incomingMail = await _unitOfWork.IncomingMails.GetByIdAsync(id);
                if (incomingMail == null) return false;

                _unitOfWork.IncomingMails.Remove(incomingMail);
                await _unitOfWork.SaveChangesAsync();

                await LogActivityAsync(incomingMail.CreatedByUserId, "حذف", "IncomingMails", id);

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<IEnumerable<IncomingMail>> SearchAsync(string? searchTerm, string? senderEntity, 
            DateTime? fromDate, DateTime? toDate, string? status, string? priority)
        {
            var query = await _unitOfWork.IncomingMails.GetAllAsync();
            var result = query.AsQueryable();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                result = result.Where(m => m.Subject.Contains(searchTerm) || 
                                          m.IncomingNumber.Contains(searchTerm) ||
                                          (m.Content != null && m.Content.Contains(searchTerm)));
            }

            if (!string.IsNullOrEmpty(senderEntity))
            {
                result = result.Where(m => m.SenderEntity.Contains(senderEntity));
            }

            if (fromDate.HasValue)
            {
                result = result.Where(m => m.ReceivedDate >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                result = result.Where(m => m.ReceivedDate <= toDate.Value);
            }

            if (!string.IsNullOrEmpty(status))
            {
                result = result.Where(m => m.Status == status);
            }

            if (!string.IsNullOrEmpty(priority))
            {
                result = result.Where(m => m.Priority == priority);
            }

            return result.OrderByDescending(m => m.ReceivedDate).ToList();
        }

        public async Task<IEnumerable<IncomingMail>> GetPagedAsync(int pageNumber, int pageSize, 
            string? searchTerm = null, string? status = null)
        {
            return await _unitOfWork.IncomingMails.GetPagedAsync(
                pageNumber, 
                pageSize, 
                filter: m => (string.IsNullOrEmpty(searchTerm) || m.Subject.Contains(searchTerm) || m.IncomingNumber.Contains(searchTerm)) &&
                            (string.IsNullOrEmpty(status) || m.Status == status),
                orderBy: q => q.OrderByDescending(m => m.ReceivedDate),
                includeProperties: "CreatedByUser,AssignedToUser,Attachments"
            );
        }

        public async Task<bool> UpdateStatusAsync(int id, string status)
        {
            try
            {
                var incomingMail = await _unitOfWork.IncomingMails.GetByIdAsync(id);
                if (incomingMail == null) return false;

                var oldStatus = incomingMail.Status;
                incomingMail.Status = status;
                incomingMail.UpdatedDate = DateTime.Now;

                _unitOfWork.IncomingMails.Update(incomingMail);
                await _unitOfWork.SaveChangesAsync();

                await LogActivityAsync(incomingMail.CreatedByUserId, "تعديل", "IncomingMails", id, 
                    $"تغيير الحالة من {oldStatus} إلى {status}");

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> AssignToUserAsync(int id, int userId)
        {
            try
            {
                var incomingMail = await _unitOfWork.IncomingMails.GetByIdAsync(id);
                if (incomingMail == null) return false;

                incomingMail.AssignedToUserId = userId;
                incomingMail.UpdatedDate = DateTime.Now;

                _unitOfWork.IncomingMails.Update(incomingMail);
                await _unitOfWork.SaveChangesAsync();

                await LogActivityAsync(incomingMail.CreatedByUserId, "تعديل", "IncomingMails", id, 
                    $"تعيين البريد للمستخدم {userId}");

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<string> GenerateIncomingNumberAsync()
        {
            var year = DateTime.Now.Year;
            var count = await _unitOfWork.IncomingMails.CountAsync(m => m.CreatedDate.Year == year);
            return $"IN-{year}-{(count + 1):D6}";
        }

        public async Task<IEnumerable<IncomingMail>> GetByUserAsync(int userId)
        {
            return await _unitOfWork.IncomingMails.FindAsync(m => m.AssignedToUserId == userId);
        }

        public async Task<IEnumerable<IncomingMail>> GetPendingAsync()
        {
            return await _unitOfWork.IncomingMails.FindAsync(m => m.Status == "جديد" || m.Status == "قيد الإجراء");
        }

        private async Task LogActivityAsync(int userId, string action, string tableName, int recordId, string? description = null)
        {
            try
            {
                var log = new Log
                {
                    UserId = userId,
                    Action = action,
                    TableName = tableName,
                    RecordId = recordId,
                    NewValues = description,
                    IPAddress = "127.0.0.1",
                    CreatedDate = DateTime.Now
                };

                await _unitOfWork.Logs.AddAsync(log);
                await _unitOfWork.SaveChangesAsync();
            }
            catch (Exception)
            {
                // Log errors should not break the main functionality
            }
        }
    }
}
