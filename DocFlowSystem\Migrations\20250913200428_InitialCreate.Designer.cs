﻿// <auto-generated />
using System;
using DocFlowSystem.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace DocFlowSystem.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250913200428_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.11");

            modelBuilder.Entity("DocFlowSystem.Models.Attachment", b =>
                {
                    b.Property<int>("AttachmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<long>("FileSize")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int?>("IncomingMailId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<int?>("OutgoingMailId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("UploadedByUserId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UploadedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.HasKey("AttachmentId");

                    b.HasIndex("IncomingMailId");

                    b.HasIndex("OutgoingMailId");

                    b.HasIndex("UploadedByUserId");

                    b.ToTable("Attachments", t =>
                        {
                            t.HasCheckConstraint("CHK_Attachment_Mail", "(IncomingMailId IS NOT NULL AND OutgoingMailId IS NULL) OR (IncomingMailId IS NULL AND OutgoingMailId IS NOT NULL)");
                        });
                });

            modelBuilder.Entity("DocFlowSystem.Models.IncomingMail", b =>
                {
                    b.Property<int>("IncomingMailId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("AssignedToUserId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Content")
                        .HasColumnType("TEXT");

                    b.Property<int>("CreatedByUserId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<string>("IncomingNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("متوسط");

                    b.Property<DateTime>("ReceivedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("SenderEntity")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("جديد");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("IncomingMailId");

                    b.HasIndex("AssignedToUserId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("IncomingNumber")
                        .IsUnique();

                    b.HasIndex("ReceivedDate");

                    b.HasIndex("SenderEntity");

                    b.HasIndex("Status");

                    b.ToTable("IncomingMails");
                });

            modelBuilder.Entity("DocFlowSystem.Models.Log", b =>
                {
                    b.Property<int>("LogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<string>("IPAddress")
                        .HasMaxLength(45)
                        .HasColumnType("TEXT");

                    b.Property<string>("NewValues")
                        .HasColumnType("TEXT");

                    b.Property<string>("OldValues")
                        .HasColumnType("TEXT");

                    b.Property<int?>("RecordId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("TableName")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("LogId");

                    b.HasIndex("CreatedDate");

                    b.HasIndex("UserId");

                    b.HasIndex("TableName", "RecordId");

                    b.ToTable("Logs");
                });

            modelBuilder.Entity("DocFlowSystem.Models.OutgoingMail", b =>
                {
                    b.Property<int>("OutgoingMailId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Content")
                        .HasColumnType("TEXT");

                    b.Property<int>("CreatedByUserId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<string>("OutgoingNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("متوسط");

                    b.Property<string>("RecipientEntity")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<int?>("RelatedIncomingMailId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("SentDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("مسودة");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.HasKey("OutgoingMailId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("OutgoingNumber")
                        .IsUnique();

                    b.HasIndex("RecipientEntity");

                    b.HasIndex("RelatedIncomingMailId");

                    b.HasIndex("SentDate");

                    b.HasIndex("Status");

                    b.ToTable("OutgoingMails");
                });

            modelBuilder.Entity("DocFlowSystem.Models.User", b =>
                {
                    b.Property<int>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<string>("Department")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT");

                    b.Property<int>("RoleId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("UserId");

                    b.HasIndex("RoleId");

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users");

                    b.HasData(
                        new
                        {
                            UserId = 1,
                            CreatedDate = new DateTime(2025, 9, 13, 22, 4, 27, 554, DateTimeKind.Local).AddTicks(2974),
                            Department = "إدارة تقنية المعلومات",
                            Email = "<EMAIL>",
                            FullName = "مدير النظام",
                            IsActive = true,
                            PasswordHash = "$2a$11$rQZr5EZ5Z5Z5Z5Z5Z5Z5ZOK5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5",
                            RoleId = 1,
                            Username = "admin"
                        });
                });

            modelBuilder.Entity("DocFlowSystem.Models.UserRole", b =>
                {
                    b.Property<int>("RoleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true);

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("RoleId");

                    b.HasIndex("RoleName")
                        .IsUnique();

                    b.ToTable("UserRoles");

                    b.HasData(
                        new
                        {
                            RoleId = 1,
                            CreatedDate = new DateTime(2025, 9, 13, 22, 4, 27, 554, DateTimeKind.Local).AddTicks(2828),
                            Description = "مدير النظام - صلاحيات كاملة",
                            IsActive = true,
                            RoleName = "مدير"
                        },
                        new
                        {
                            RoleId = 2,
                            CreatedDate = new DateTime(2025, 9, 13, 22, 4, 27, 554, DateTimeKind.Local).AddTicks(2830),
                            Description = "مستخدم عادي - صلاحيات محدودة",
                            IsActive = true,
                            RoleName = "مستخدم"
                        },
                        new
                        {
                            RoleId = 3,
                            CreatedDate = new DateTime(2025, 9, 13, 22, 4, 27, 554, DateTimeKind.Local).AddTicks(2832),
                            Description = "مسؤول الأرشيف - صلاحيات الأرشفة والبحث",
                            IsActive = true,
                            RoleName = "أرشيف"
                        });
                });

            modelBuilder.Entity("DocFlowSystem.Models.Attachment", b =>
                {
                    b.HasOne("DocFlowSystem.Models.IncomingMail", "IncomingMail")
                        .WithMany("Attachments")
                        .HasForeignKey("IncomingMailId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("DocFlowSystem.Models.OutgoingMail", "OutgoingMail")
                        .WithMany("Attachments")
                        .HasForeignKey("OutgoingMailId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("DocFlowSystem.Models.User", "UploadedByUser")
                        .WithMany("UploadedAttachments")
                        .HasForeignKey("UploadedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("IncomingMail");

                    b.Navigation("OutgoingMail");

                    b.Navigation("UploadedByUser");
                });

            modelBuilder.Entity("DocFlowSystem.Models.IncomingMail", b =>
                {
                    b.HasOne("DocFlowSystem.Models.User", "AssignedToUser")
                        .WithMany("AssignedIncomingMails")
                        .HasForeignKey("AssignedToUserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("DocFlowSystem.Models.User", "CreatedByUser")
                        .WithMany("CreatedIncomingMails")
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("AssignedToUser");

                    b.Navigation("CreatedByUser");
                });

            modelBuilder.Entity("DocFlowSystem.Models.Log", b =>
                {
                    b.HasOne("DocFlowSystem.Models.User", "User")
                        .WithMany("Logs")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("DocFlowSystem.Models.OutgoingMail", b =>
                {
                    b.HasOne("DocFlowSystem.Models.User", "CreatedByUser")
                        .WithMany("CreatedOutgoingMails")
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("DocFlowSystem.Models.IncomingMail", "RelatedIncomingMail")
                        .WithMany("RelatedOutgoingMails")
                        .HasForeignKey("RelatedIncomingMailId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("CreatedByUser");

                    b.Navigation("RelatedIncomingMail");
                });

            modelBuilder.Entity("DocFlowSystem.Models.User", b =>
                {
                    b.HasOne("DocFlowSystem.Models.UserRole", "Role")
                        .WithMany("Users")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("DocFlowSystem.Models.IncomingMail", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("RelatedOutgoingMails");
                });

            modelBuilder.Entity("DocFlowSystem.Models.OutgoingMail", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("DocFlowSystem.Models.User", b =>
                {
                    b.Navigation("AssignedIncomingMails");

                    b.Navigation("CreatedIncomingMails");

                    b.Navigation("CreatedOutgoingMails");

                    b.Navigation("Logs");

                    b.Navigation("UploadedAttachments");
                });

            modelBuilder.Entity("DocFlowSystem.Models.UserRole", b =>
                {
                    b.Navigation("Users");
                });
#pragma warning restore 612, 618
        }
    }
}
