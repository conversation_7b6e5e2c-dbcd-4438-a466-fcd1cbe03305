<UserControl x:Class="DocFlowSystem.Views.Users.EditUserView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             FlowDirection="RightToLeft"
             d:DesignHeight="600" d:DesignWidth="700">
    
    <UserControl.Resources>
        <Style x:Key="HeaderTextBlock" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>
        
        <Style x:Key="LabelTextBlock" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
            <Setter Property="VerticalAlignment" Value="Top"/>
        </Style>
        
        <Style x:Key="InputControl" TargetType="Control">
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Height" Value="35"/>
        </Style>
        
        <Style x:Key="SaveButton" TargetType="Button">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
        </Style>
        
        <Style x:Key="CancelButton" TargetType="Button">
            <Setter Property="Background" Value="#F44336"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                <Button x:Name="BackButton" Content="← رجوع" 
                       Background="#607D8B" Foreground="White" 
                       BorderThickness="0" Padding="15,8" 
                       Click="BackButton_Click" Margin="0,0,20,0"/>
                <TextBlock x:Name="HeaderTextBlock" Text="✏️ تعديل المستخدم" Style="{StaticResource HeaderTextBlock}"/>
            </StackPanel>

            <!-- Form -->
            <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="30" Margin="0,0,0,20">
                <Border.Effect>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
                </Border.Effect>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Left Column -->
                    <StackPanel Grid.Column="0">
                        <!-- Username -->
                        <TextBlock Text="اسم المستخدم: *" Style="{StaticResource LabelTextBlock}"/>
                        <TextBox x:Name="UsernameTextBox" Style="{StaticResource InputControl}"/>

                        <!-- Full Name -->
                        <TextBlock Text="الاسم الكامل: *" Style="{StaticResource LabelTextBlock}"/>
                        <TextBox x:Name="FullNameTextBox" Style="{StaticResource InputControl}"/>

                        <!-- Email -->
                        <TextBlock Text="البريد الإلكتروني:" Style="{StaticResource LabelTextBlock}"/>
                        <TextBox x:Name="EmailTextBox" Style="{StaticResource InputControl}"/>

                        <!-- Phone -->
                        <TextBlock Text="رقم الهاتف:" Style="{StaticResource LabelTextBlock}"/>
                        <TextBox x:Name="PhoneTextBox" Style="{StaticResource InputControl}"/>
                    </StackPanel>

                    <!-- Right Column -->
                    <StackPanel Grid.Column="2">
                        <!-- Role -->
                        <TextBlock Text="الدور: *" Style="{StaticResource LabelTextBlock}"/>
                        <ComboBox x:Name="RoleComboBox" 
                                 Style="{StaticResource InputControl}"
                                 DisplayMemberPath="RoleName"
                                 SelectedValuePath="UserRoleId"/>

                        <!-- Department -->
                        <TextBlock Text="القسم:" Style="{StaticResource LabelTextBlock}"/>
                        <TextBox x:Name="DepartmentTextBox" Style="{StaticResource InputControl}"/>

                        <!-- Is Active -->
                        <CheckBox x:Name="IsActiveCheckBox" 
                                 Content="المستخدم نشط" 
                                 FontWeight="Bold" 
                                 Margin="0,10,0,15"/>

                        <!-- Change Password Section -->
                        <Border Background="#FFF3E0" CornerRadius="5" Padding="15" Margin="0,10,0,0">
                            <StackPanel>
                                <TextBlock Text="🔑 تغيير كلمة المرور" FontWeight="Bold" Margin="0,0,0,10"/>
                                
                                <CheckBox x:Name="ChangePasswordCheckBox" 
                                         Content="تغيير كلمة المرور" 
                                         Margin="0,0,0,10"
                                         Checked="ChangePasswordCheckBox_Checked"
                                         Unchecked="ChangePasswordCheckBox_Unchecked"/>
                                
                                <StackPanel x:Name="PasswordPanel" Visibility="Collapsed">
                                    <TextBlock Text="كلمة المرور الجديدة:" FontWeight="Bold" Margin="0,0,0,5"/>
                                    <PasswordBox x:Name="NewPasswordBox" Margin="0,0,0,15" Padding="8" Height="35"/>

                                    <TextBlock Text="تأكيد كلمة المرور:" FontWeight="Bold" Margin="0,0,0,5"/>
                                    <PasswordBox x:Name="ConfirmPasswordBox" Margin="0,0,0,15" Padding="8" Height="35"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Action Buttons -->
            <Border Grid.Row="2" Background="White" CornerRadius="8" Padding="20">
                <Border.Effect>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
                </Border.Effect>
                
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="SaveButton" Content="💾 حفظ التغييرات" 
                           Style="{StaticResource SaveButton}"
                           Click="SaveButton_Click"/>
                    
                    <Button x:Name="CancelButton" Content="❌ إلغاء" 
                           Style="{StaticResource CancelButton}"
                           Click="CancelButton_Click"/>
                </StackPanel>
            </Border>

            <!-- Loading Indicator -->
            <Border x:Name="LoadingBorder" 
                   Grid.RowSpan="3"
                   Background="#80000000" 
                   Visibility="Collapsed">
                <Border HorizontalAlignment="Center" 
                       VerticalAlignment="Center"
                       Background="White" 
                       Padding="30" 
                       CornerRadius="10">
                    <StackPanel>
                        <ProgressBar Width="200" Height="20" IsIndeterminate="True" Margin="0,0,0,15"/>
                        <TextBlock x:Name="LoadingTextBlock" 
                                  Text="جاري الحفظ..." 
                                  HorizontalAlignment="Center" 
                                  FontWeight="Bold"/>
                    </StackPanel>
                </Border>
            </Border>
        </Grid>
    </ScrollViewer>
</UserControl>
