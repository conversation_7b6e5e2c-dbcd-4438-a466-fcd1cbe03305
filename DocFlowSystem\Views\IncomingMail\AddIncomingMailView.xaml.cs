using System.Windows;
using System.Windows.Controls;
using DocFlowSystem.Services.Interfaces;
using DocFlowSystem.Models;
using DocFlowSystem.Helpers;

namespace DocFlowSystem.Views.IncomingMail
{
    public partial class AddIncomingMailView : UserControl
    {
        private readonly IIncomingMailService _incomingMailService;
        private readonly IServiceProvider _serviceProvider;
        public event EventHandler? IncomingMailSaved;

        public AddIncomingMailView(IIncomingMailService incomingMailService, IServiceProvider serviceProvider)
        {
            InitializeComponent();
            _incomingMailService = incomingMailService;
            _serviceProvider = serviceProvider;
            
            Loaded += AddIncomingMailView_Loaded;
        }

        private async void AddIncomingMailView_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadUsersAsync();
            await GenerateIncomingNumberAsync();
        }

        private async Task LoadUsersAsync()
        {
            try
            {
                // Get users from service - for now we'll use a simple approach
                var users = new List<User>
                {
                    new User { UserId = SessionManager.CurrentUserId, FullName = SessionManager.CurrentUserName }
                };

                AssignedToUserComboBox.ItemsSource = users;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل قائمة المستخدمين: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task GenerateIncomingNumberAsync()
        {
            try
            {
                var incomingNumber = await _incomingMailService.GenerateIncomingNumberAsync();
                IncomingNumberTextBox.Text = incomingNumber;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء الرقم الوارد: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            await SaveIncomingMailAsync(false);
        }

        private async void SaveAndNewButton_Click(object sender, RoutedEventArgs e)
        {
            await SaveIncomingMailAsync(true);
        }

        private async Task SaveIncomingMailAsync(bool saveAndNew)
        {
            if (!ValidateForm()) return;

            try
            {
                ShowLoading(true, "جاري الحفظ...");

                var incomingMail = new Models.IncomingMail
                {
                    IncomingNumber = IncomingNumberTextBox.Text.Trim(),
                    SenderEntity = SenderEntityTextBox.Text.Trim(),
                    Subject = SubjectTextBox.Text.Trim(),
                    Content = ContentTextBox.Text?.Trim(),
                    ReceivedDate = ReceivedDatePicker.SelectedDate ?? DateTime.Now,
                    Priority = GetSelectedComboBoxValue(PriorityComboBox) ?? "متوسط",
                    Status = GetSelectedComboBoxValue(StatusComboBox) ?? "جديد",
                    AssignedToUserId = AssignedToUserComboBox.SelectedValue as int?,
                    CreatedByUserId = SessionManager.CurrentUserId,
                    Notes = NotesTextBox.Text?.Trim()
                };

                var savedMail = await _incomingMailService.CreateAsync(incomingMail);

                MessageBox.Show("تم حفظ البريد الوارد بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);

                IncomingMailSaved?.Invoke(this, EventArgs.Empty);

                if (saveAndNew)
                {
                    ClearForm();
                    await GenerateIncomingNumberAsync();
                }
                else
                {
                    // Navigate back to list or close
                    IncomingMailSaved?.Invoke(this, EventArgs.Empty);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البريد الوارد: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(SenderEntityTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الجهة المرسلة", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                SenderEntityTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(SubjectTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الموضوع", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                SubjectTextBox.Focus();
                return false;
            }

            if (!ReceivedDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ الاستلام", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                ReceivedDatePicker.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            SenderEntityTextBox.Clear();
            SubjectTextBox.Clear();
            ContentTextBox.Clear();
            NotesTextBox.Clear();
            ReceivedDatePicker.SelectedDate = DateTime.Now;
            PriorityComboBox.SelectedIndex = 1; // متوسط
            StatusComboBox.SelectedIndex = 0; // جديد
            AssignedToUserComboBox.SelectedIndex = -1;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إلغاء إضافة البريد الوارد؟\n\nسيتم فقدان جميع البيانات المدخلة.", 
                "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                IncomingMailSaved?.Invoke(this, EventArgs.Empty); // Navigate back
            }
        }

        private void ShowLoading(bool show, string message = "جاري التحميل...")
        {
            LoadingBorder.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            LoadingTextBlock.Text = message;
            
            // Disable form controls
            SenderEntityTextBox.IsEnabled = !show;
            SubjectTextBox.IsEnabled = !show;
            ContentTextBox.IsEnabled = !show;
            NotesTextBox.IsEnabled = !show;
            ReceivedDatePicker.IsEnabled = !show;
            PriorityComboBox.IsEnabled = !show;
            StatusComboBox.IsEnabled = !show;
            AssignedToUserComboBox.IsEnabled = !show;
            SaveButton.IsEnabled = !show;
            SaveAndNewButton.IsEnabled = !show;
            CancelButton.IsEnabled = !show;
        }

        private string? GetSelectedComboBoxValue(ComboBox comboBox)
        {
            if (comboBox.SelectedItem is ComboBoxItem item)
            {
                return item.Content.ToString();
            }
            return null;
        }
    }
}
