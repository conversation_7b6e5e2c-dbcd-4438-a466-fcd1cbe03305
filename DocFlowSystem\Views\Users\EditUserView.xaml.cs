using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DocFlowSystem.Services.Interfaces;
using DocFlowSystem.Models;
using DocFlowSystem.Helpers;
using Microsoft.Extensions.DependencyInjection;
using System.Text.RegularExpressions;

namespace DocFlowSystem.Views.Users
{
    public partial class EditUserView : UserControl
    {
        private readonly User _user;
        private readonly IUserService _userService;
        private readonly IServiceProvider _serviceProvider;
        public event EventHandler? UserUpdated;

        public EditUserView(User user, IUserService userService, IServiceProvider serviceProvider)
        {
            InitializeComponent();
            _user = user;
            _userService = userService;
            _serviceProvider = serviceProvider;
            
            Loaded += EditUserView_Loaded;
        }

        private async void EditUserView_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadRolesAsync();
            LoadUserData();
        }

        private async Task LoadRolesAsync()
        {
            try
            {
                var userRoleService = _serviceProvider.GetRequiredService<IUserRoleService>();
                var roles = await userRoleService.GetAllAsync();
                
                RoleComboBox.ItemsSource = roles;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الأدوار: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadUserData()
        {
            HeaderTextBlock.Text = $"✏️ تعديل المستخدم: {_user.FullName}";
            
            UsernameTextBox.Text = _user.Username;
            FullNameTextBox.Text = _user.FullName;
            EmailTextBox.Text = _user.Email ?? "";
            PhoneTextBox.Text = _user.Phone ?? "";
            DepartmentTextBox.Text = _user.Department ?? "";
            IsActiveCheckBox.IsChecked = _user.IsActive;
            
            // Select current role
            if (_user.UserRoleId > 0)
            {
                RoleComboBox.SelectedValue = _user.UserRoleId;
            }
        }

        private void ChangePasswordCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            PasswordPanel.Visibility = Visibility.Visible;
        }

        private void ChangePasswordCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            PasswordPanel.Visibility = Visibility.Collapsed;
            NewPasswordBox.Clear();
            ConfirmPasswordBox.Clear();
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm()) return;

            try
            {
                ShowLoading(true, "جاري حفظ التغييرات...");

                // Update user data
                _user.Username = UsernameTextBox.Text.Trim();
                _user.FullName = FullNameTextBox.Text.Trim();
                _user.Email = string.IsNullOrWhiteSpace(EmailTextBox.Text) ? null : EmailTextBox.Text.Trim();
                _user.Phone = string.IsNullOrWhiteSpace(PhoneTextBox.Text) ? null : PhoneTextBox.Text.Trim();
                _user.Department = string.IsNullOrWhiteSpace(DepartmentTextBox.Text) ? null : DepartmentTextBox.Text.Trim();
                _user.UserRoleId = (int)RoleComboBox.SelectedValue;
                _user.IsActive = IsActiveCheckBox.IsChecked ?? true;
                _user.UpdatedDate = DateTime.Now;
                _user.UpdatedByUserId = SessionManager.CurrentUserId;

                var updatedUser = await _userService.UpdateAsync(_user);

                // Change password if requested
                if (ChangePasswordCheckBox.IsChecked == true && !string.IsNullOrEmpty(NewPasswordBox.Password))
                {
                    // For admin editing other users, we'll reset the password directly
                    await _userService.ResetPasswordAsync(_user.UserId);
                    
                    // Update the user's password with the new one
                    // This is a simplified approach - in a real system you might want a different flow
                    var tempPassword = await _userService.ResetPasswordAsync(_user.UserId);
                    MessageBox.Show($"تم تحديث كلمة المرور. كلمة المرور المؤقتة: {tempPassword}", "تم تحديث كلمة المرور", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }

                MessageBox.Show("تم تحديث بيانات المستخدم بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);

                UserUpdated?.Invoke(this, EventArgs.Empty);

                // Navigate back to user management
                BackButton_Click(this, new RoutedEventArgs());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ التغييرات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private bool ValidateForm()
        {
            // Username validation
            if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                UsernameTextBox.Focus();
                return false;
            }

            if (UsernameTextBox.Text.Length < 3)
            {
                MessageBox.Show("اسم المستخدم يجب أن يكون 3 أحرف على الأقل", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                UsernameTextBox.Focus();
                return false;
            }

            // Full name validation
            if (string.IsNullOrWhiteSpace(FullNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الكامل", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                FullNameTextBox.Focus();
                return false;
            }

            // Email validation
            if (!string.IsNullOrWhiteSpace(EmailTextBox.Text) && !IsValidEmail(EmailTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                EmailTextBox.Focus();
                return false;
            }

            // Role validation
            if (RoleComboBox.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار دور للمستخدم", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                RoleComboBox.Focus();
                return false;
            }

            // Password validation (if changing password)
            if (ChangePasswordCheckBox.IsChecked == true)
            {
                if (string.IsNullOrWhiteSpace(NewPasswordBox.Password))
                {
                    MessageBox.Show("يرجى إدخال كلمة المرور الجديدة", "تحذير", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    NewPasswordBox.Focus();
                    return false;
                }

                if (NewPasswordBox.Password.Length < 6)
                {
                    MessageBox.Show("كلمة المرور يجب أن تكون 6 أحرف على الأقل", "تحذير", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    NewPasswordBox.Focus();
                    return false;
                }

                if (NewPasswordBox.Password != ConfirmPasswordBox.Password)
                {
                    MessageBox.Show("كلمة المرور وتأكيد كلمة المرور غير متطابقتين", "تحذير", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    ConfirmPasswordBox.Focus();
                    return false;
                }
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
                return emailRegex.IsMatch(email);
            }
            catch
            {
                return false;
            }
        }

        private void BackButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var userService = _serviceProvider.GetRequiredService<IUserService>();
                var userManagementView = new UserManagementView(userService, _serviceProvider);
                
                var parentFrame = FindParent<Frame>(this);
                if (parentFrame != null)
                {
                    parentFrame.Content = userManagementView;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء العودة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إلغاء التعديل؟\n\nسيتم فقدان جميع التغييرات غير المحفوظة.", 
                "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                BackButton_Click(sender, e);
            }
        }

        private void ShowLoading(bool show, string message = "جاري المعالجة...")
        {
            LoadingBorder.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            LoadingTextBlock.Text = message;
            
            // Disable form controls
            UsernameTextBox.IsEnabled = !show;
            FullNameTextBox.IsEnabled = !show;
            EmailTextBox.IsEnabled = !show;
            PhoneTextBox.IsEnabled = !show;
            DepartmentTextBox.IsEnabled = !show;
            RoleComboBox.IsEnabled = !show;
            IsActiveCheckBox.IsEnabled = !show;
            ChangePasswordCheckBox.IsEnabled = !show;
            NewPasswordBox.IsEnabled = !show;
            ConfirmPasswordBox.IsEnabled = !show;
            SaveButton.IsEnabled = !show;
            CancelButton.IsEnabled = !show;
        }

        private T? FindParent<T>(DependencyObject child) where T : DependencyObject
        {
            var parentObject = VisualTreeHelper.GetParent(child);
            if (parentObject == null) return null;
            
            if (parentObject is T parent)
                return parent;
            
            return FindParent<T>(parentObject);
        }
    }
}
