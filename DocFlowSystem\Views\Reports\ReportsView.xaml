<UserControl x:Class="DocFlowSystem.Views.Reports.ReportsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             FlowDirection="RightToLeft"
             d:DesignHeight="600" d:DesignWidth="900">
    
    <UserControl.Resources>
        <Style x:Key="HeaderTextBlock" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>
        
        <Style x:Key="ReportButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,15"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="MinWidth" Value="200"/>
            <Setter Property="MinHeight" Value="80"/>
        </Style>
        
        <Style x:Key="ExportButton" TargetType="Button">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="📊 التقارير والإحصائيات" Style="{StaticResource HeaderTextBlock}"/>

        <!-- Report Selection -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
            </Border.Effect>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="اختر نوع التقرير:" FontWeight="Bold" FontSize="14" Margin="0,0,0,15"/>
                
                <WrapPanel Grid.Row="1" Orientation="Horizontal">
                    <Button x:Name="IncomingMailReportButton" Content="📥&#x0A;تقرير البريد الوارد" 
                           Style="{StaticResource ReportButton}"
                           Click="IncomingMailReportButton_Click"/>
                    
                    <Button x:Name="OutgoingMailReportButton" Content="📤&#x0A;تقرير البريد الصادر" 
                           Style="{StaticResource ReportButton}"
                           Click="OutgoingMailReportButton_Click"/>
                    
                    <Button x:Name="StatisticsReportButton" Content="📈&#x0A;الإحصائيات العامة" 
                           Style="{StaticResource ReportButton}"
                           Background="#4CAF50"
                           Click="StatisticsReportButton_Click"/>
                    
                    <Button x:Name="UserActivityReportButton" Content="👥&#x0A;نشاط المستخدمين" 
                           Style="{StaticResource ReportButton}"
                           Background="#FF9800"
                           Click="UserActivityReportButton_Click"/>
                </WrapPanel>
            </Grid>
        </Border>

        <!-- Report Content -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" Padding="20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
            </Border.Effect>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Report Title -->
                <TextBlock x:Name="ReportTitleTextBlock" 
                          Grid.Row="0"
                          Text="اختر نوع التقرير من الأعلى"
                          FontWeight="Bold" 
                          FontSize="14" 
                          Margin="0,0,0,15"/>

                <!-- Report Data -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel x:Name="ReportContentPanel">
                        <!-- Default Content -->
                        <Border Background="#F5F5F5" 
                               CornerRadius="5" 
                               Padding="30" 
                               HorizontalAlignment="Center" 
                               VerticalAlignment="Center">
                            <StackPanel HorizontalAlignment="Center">
                                <TextBlock Text="📊" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                <TextBlock Text="اختر نوع التقرير المطلوب من الأزرار أعلاه" 
                                          FontSize="16" 
                                          HorizontalAlignment="Center" 
                                          Foreground="Gray"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </Border>

        <!-- Export Panel -->
        <Border Grid.Row="3" Background="White" CornerRadius="8" Padding="15" Margin="0,20,0,0">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
            </Border.Effect>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                <Button x:Name="ExportToPdfButton" Content="📄 تصدير PDF" 
                       Style="{StaticResource ExportButton}"
                       Background="#F44336" Foreground="White"
                       Click="ExportToPdfButton_Click" 
                       IsEnabled="False"/>
                
                <Button x:Name="ExportToExcelButton" Content="📊 تصدير Excel" 
                       Style="{StaticResource ExportButton}"
                       Background="#4CAF50" Foreground="White"
                       Click="ExportToExcelButton_Click" 
                       IsEnabled="False"/>
                
                <Button x:Name="PrintReportButton" Content="🖨️ طباعة" 
                       Style="{StaticResource ExportButton}"
                       Background="#607D8B" Foreground="White"
                       Click="PrintReportButton_Click" 
                       IsEnabled="False"/>
                
                <TextBlock x:Name="StatusTextBlock" Text="جاهز" 
                          VerticalAlignment="Center" 
                          FontStyle="Italic" 
                          Foreground="Gray" 
                          Margin="20,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- Loading Indicator -->
        <Border x:Name="LoadingBorder" 
               Grid.RowSpan="4"
               Background="#80000000" 
               Visibility="Collapsed">
            <Border HorizontalAlignment="Center" 
                   VerticalAlignment="Center"
                   Background="White" 
                   Padding="30" 
                   CornerRadius="10">
                <StackPanel>
                    <ProgressBar Width="200" Height="20" IsIndeterminate="True" Margin="0,0,0,15"/>
                    <TextBlock x:Name="LoadingTextBlock" 
                              Text="جاري إنشاء التقرير..." 
                              HorizontalAlignment="Center" 
                              FontWeight="Bold"/>
                </StackPanel>
            </Border>
        </Border>
    </Grid>
</UserControl>
