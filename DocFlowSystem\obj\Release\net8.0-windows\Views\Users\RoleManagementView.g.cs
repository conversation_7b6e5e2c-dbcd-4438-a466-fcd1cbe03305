﻿#pragma checksum "..\..\..\..\..\Views\Users\RoleManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5CD3D098804ACF7471228DCDD15ABD4251FEF23F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DocFlowSystem.Views.Users {
    
    
    /// <summary>
    /// RoleManagementView
    /// </summary>
    public partial class RoleManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 35 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BackButton;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid RolesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PermissionsPanel;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoRoleSelectedText;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PermissionsList;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedRoleName;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanViewIncomingMailCheckBox;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanAddIncomingMailCheckBox;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanEditIncomingMailCheckBox;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanDeleteIncomingMailCheckBox;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanViewOutgoingMailCheckBox;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanAddOutgoingMailCheckBox;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanEditOutgoingMailCheckBox;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanDeleteOutgoingMailCheckBox;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanManageUsersCheckBox;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanViewReportsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanManageArchiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CanManageBackupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SavePermissionsButton;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddRoleButton;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LoadingBorder;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.9.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DocFlowSystem;component/views/users/rolemanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.9.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BackButton = ((System.Windows.Controls.Button)(target));
            
            #line 38 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
            this.BackButton.Click += new System.Windows.RoutedEventHandler(this.BackButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RolesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 69 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
            this.RolesDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.RolesDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.PermissionsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 6:
            this.NoRoleSelectedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.PermissionsList = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.SelectedRoleName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.CanViewIncomingMailCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.CanAddIncomingMailCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.CanEditIncomingMailCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.CanDeleteIncomingMailCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.CanViewOutgoingMailCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            this.CanAddOutgoingMailCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 15:
            this.CanEditOutgoingMailCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.CanDeleteOutgoingMailCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.CanManageUsersCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 18:
            this.CanViewReportsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 19:
            this.CanManageArchiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.CanManageBackupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.SavePermissionsButton = ((System.Windows.Controls.Button)(target));
            
            #line 137 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
            this.SavePermissionsButton.Click += new System.Windows.RoutedEventHandler(this.SavePermissionsButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.AddRoleButton = ((System.Windows.Controls.Button)(target));
            
            #line 156 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
            this.AddRoleButton.Click += new System.Windows.RoutedEventHandler(this.AddRoleButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 161 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.LoadingBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 26:
            this.LoadingTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.9.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 3:
            
            #line 83 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditRoleButton_Click);
            
            #line default
            #line hidden
            break;
            case 4:
            
            #line 87 "..\..\..\..\..\Views\Users\RoleManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteRoleButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

