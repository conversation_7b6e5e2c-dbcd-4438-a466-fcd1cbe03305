using DocFlowSystem.Data.Repositories;
using DocFlowSystem.Services.Interfaces;

namespace DocFlowSystem.Services.Implementations
{
    public class DatabaseInitializerService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAuthenticationService _authenticationService;

        public DatabaseInitializerService(IUnitOfWork unitOfWork, IAuthenticationService authenticationService)
        {
            _unitOfWork = unitOfWork;
            _authenticationService = authenticationService;
        }

        public async Task InitializeAsync()
        {
            try
            {
                // Check if admin user exists and update password if needed
                var adminUser = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Username == "admin");
                if (adminUser != null)
                {
                    // Update password hash for admin user (password: admin)
                    var correctPasswordHash = _authenticationService.HashPassword("admin");

                    // Always update the password hash to ensure it's correct
                    adminUser.PasswordHash = correctPasswordHash;
                    _unitOfWork.Users.Update(adminUser);
                    await _unitOfWork.SaveChangesAsync();

                    Console.WriteLine("Admin user password updated successfully.");
                }

                // Add sample data if needed
                await AddSampleDataAsync();
            }
            catch (Exception ex)
            {
                // Log error but don't throw to prevent app startup failure
                Console.WriteLine($"Database initialization error: {ex.Message}");
            }
        }

        private async Task AddSampleDataAsync()
        {
            // Check if we already have sample data
            var incomingMailCount = await _unitOfWork.IncomingMails.CountAsync();
            if (incomingMailCount > 0) return; // Sample data already exists

            var adminUser = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Username == "admin");
            if (adminUser == null) return;

            // Add sample incoming mails
            var sampleIncomingMails = new[]
            {
                new Models.IncomingMail
                {
                    IncomingNumber = "IN-2025-000001",
                    SenderEntity = "وزارة التربية والتعليم",
                    Subject = "طلب تقرير شهري عن الأنشطة التعليمية",
                    Content = "نرجو منكم إعداد تقرير شامل عن الأنشطة التعليمية للشهر الماضي وإرساله في أقرب وقت ممكن.",
                    ReceivedDate = DateTime.Now.AddDays(-5),
                    Priority = "عالي",
                    Status = "جديد",
                    CreatedByUserId = adminUser.UserId,
                    CreatedDate = DateTime.Now.AddDays(-5)
                },
                new Models.IncomingMail
                {
                    IncomingNumber = "IN-2025-000002",
                    SenderEntity = "وزارة الصحة",
                    Subject = "تعميم بخصوص الإجراءات الصحية الجديدة",
                    Content = "يرجى الاطلاع على الإجراءات الصحية الجديدة المرفقة وتطبيقها في جميع المرافق.",
                    ReceivedDate = DateTime.Now.AddDays(-3),
                    Priority = "متوسط",
                    Status = "قيد الإجراء",
                    AssignedToUserId = adminUser.UserId,
                    CreatedByUserId = adminUser.UserId,
                    CreatedDate = DateTime.Now.AddDays(-3)
                },
                new Models.IncomingMail
                {
                    IncomingNumber = "IN-2025-000003",
                    SenderEntity = "ديوان الخدمة المدنية",
                    Subject = "إشعار بتحديث اللوائح الإدارية",
                    Content = "تم تحديث اللوائح الإدارية الخاصة بالموظفين، يرجى مراجعتها وتطبيقها.",
                    ReceivedDate = DateTime.Now.AddDays(-1),
                    Priority = "منخفض",
                    Status = "منجز",
                    AssignedToUserId = adminUser.UserId,
                    CreatedByUserId = adminUser.UserId,
                    CreatedDate = DateTime.Now.AddDays(-1)
                }
            };

            await _unitOfWork.IncomingMails.AddRangeAsync(sampleIncomingMails);
            await _unitOfWork.SaveChangesAsync();

            // Add sample outgoing mails
            var sampleOutgoingMails = new[]
            {
                new Models.OutgoingMail
                {
                    OutgoingNumber = "OUT-2025-000001",
                    RecipientEntity = "وزارة التربية والتعليم",
                    Subject = "رد: طلب تقرير شهري عن الأنشطة التعليمية",
                    Content = "نتشرف بإرسال التقرير الشهري المطلوب مرفقاً مع هذا الخطاب.",
                    SentDate = DateTime.Now.AddDays(-2),
                    Priority = "عالي",
                    Status = "مرسل",
                    RelatedIncomingMailId = sampleIncomingMails[0].IncomingMailId,
                    CreatedByUserId = adminUser.UserId,
                    CreatedDate = DateTime.Now.AddDays(-2)
                },
                new Models.OutgoingMail
                {
                    OutgoingNumber = "OUT-2025-000002",
                    RecipientEntity = "جميع الإدارات",
                    Subject = "تعميم: تطبيق الإجراءات الصحية الجديدة",
                    Content = "يرجى من جميع الإدارات الالتزام بتطبيق الإجراءات الصحية الجديدة الواردة من وزارة الصحة.",
                    SentDate = DateTime.Now,
                    Priority = "متوسط",
                    Status = "مسودة",
                    CreatedByUserId = adminUser.UserId,
                    CreatedDate = DateTime.Now
                }
            };

            await _unitOfWork.OutgoingMails.AddRangeAsync(sampleOutgoingMails);
            await _unitOfWork.SaveChangesAsync();
        }
    }
}
