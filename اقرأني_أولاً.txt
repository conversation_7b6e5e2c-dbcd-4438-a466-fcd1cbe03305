========================================
    نظام إدارة البريد الحكومي
    DocFlow Management System
========================================

🎯 نظرة عامة:
============
نظام شامل لإدارة البريد الوارد والصادر في الإدارات الحكومية
مطور بتقنية C# و WPF مع قاعدة بيانات SQLite

🔧 المتطلبات:
=============
- نظام التشغيل: Windows 10 أو أحدث
- .NET 8.0 أو أحدث
- مساحة فارغة: 100 ميجابايت على الأقل

📥 التثبيت:
===========
1. تأكد من تثبيت .NET 8.0 من:
   https://dotnet.microsoft.com/download

2. فك ضغط الملفات في مجلد منفصل

3. تشغيل النظام:
   - انقر نقراً مزدوجاً على "تشغيل_نظام_إدارة_البريد.bat"
   - أو استخدم "أدوات_النظام.bat" للخيارات المتقدمة

🔐 بيانات تسجيل الدخول الافتراضية:
=================================
👤 اسم المستخدم: admin
🔑 كلمة المرور: admin123
🎯 الدور: مدير النظام (صلاحيات كاملة)

✨ المميزات الرئيسية:
==================
📥 إدارة البريد الوارد:
- إضافة وتعديل وحذف الرسائل
- بحث متقدم وتصفية
- تتبع حالة الرسائل
- إسناد المسؤوليات

📤 إدارة البريد الصادر:
- إنشاء رسائل جديدة
- ربط بالبريد الوارد
- تتبع حالة الإرسال

👥 إدارة المستخدمين:
- نظام صلاحيات متدرج
- تسجيل العمليات
- إدارة الأدوار

📊 التقارير:
- تقارير شاملة
- تصدير PDF و Excel
- إحصائيات مفصلة

🗄️ الأرشفة:
- بحث في السجلات القديمة
- نظام أرشفة ذكي
- استرجاع البيانات

🔒 الأمان:
- تشفير كلمات المرور
- تسجيل العمليات
- نظام صلاحيات محكم

🎨 واجهة المستخدم:
================
- تصميم عربي RTL
- واجهة حديثة وسهلة الاستخدام
- ألوان مريحة للعين
- استجابة سريعة

📁 هيكل الملفات:
================
📂 DocFlowSystem/
├── 📄 DocFlowSystem.csproj (ملف المشروع)
├── 📄 App.xaml (تطبيق WPF الرئيسي)
├── 📄 MainWindow.xaml (النافذة الرئيسية)
├── 📂 Models/ (نماذج البيانات)
├── 📂 Views/ (واجهات المستخدم)
├── 📂 Services/ (خدمات العمل)
├── 📂 Data/ (قاعدة البيانات)
└── 📄 DocFlowDB.db (قاعدة البيانات)

🚀 طرق التشغيل:
===============
1. الطريقة السهلة:
   انقر مزدوجاً على "تشغيل_نظام_إدارة_البريد.bat"

2. الطريقة المتقدمة:
   انقر مزدوجاً على "أدوات_النظام.bat"

3. الطريقة اليدوية:
   افتح Command Prompt في مجلد DocFlowSystem
   اكتب: dotnet run

🔧 استكشاف الأخطاء:
==================
❌ خطأ: ".NET غير مثبت"
✅ الحل: ثبت .NET 8.0 من الرابط المذكور أعلاه

❌ خطأ: "ملفات المشروع غير موجودة"
✅ الحل: تأكد من وجود مجلد DocFlowSystem

❌ خطأ: "فشل في قاعدة البيانات"
✅ الحل: استخدم "أدوات_النظام.bat" → خيار 3

❌ خطأ: "فشل تسجيل الدخول"
✅ الحل: تأكد من البيانات: admin / admin123

🆘 الدعم الفني:
===============
في حالة مواجهة مشاكل:
1. استخدم "أدوات_النظام.bat" لإعادة بناء النظام
2. تأكد من تثبيت .NET بشكل صحيح
3. تأكد من صلاحيات الكتابة في المجلد
4. أعد تشغيل الكمبيوتر إذا لزم الأمر

📝 ملاحظات مهمة:
================
- النظام يحفظ البيانات محلياً في ملف DocFlowDB.db
- يُنصح بعمل نسخة احتياطية دورية من قاعدة البيانات
- لا تحذف ملفات النظام أثناء التشغيل
- استخدم "أدوات_النظام.bat" للصيانة الدورية

🎉 استمتع باستخدام النظام!
==========================
نظام إدارة البريد الحكومي - DocFlow System
مطور بعناية لخدمة الإدارات الحكومية

تاريخ الإصدار: سبتمبر 2025
الإصدار: 1.0.0
