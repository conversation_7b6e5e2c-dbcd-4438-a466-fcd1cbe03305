using System.IO;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using DocFlowSystem.Services.Interfaces;
using DocFlowSystem.Models;
using DocFlowSystem.Helpers;
using System.Diagnostics;

namespace DocFlowSystem.Views.Attachments
{
    public partial class AttachmentManagerView : UserControl
    {
        private readonly IAttachmentService _attachmentService;
        private readonly int _relatedEntityId;
        private readonly string _entityType;
        private string? _selectedFilePath;
        private List<AttachmentViewModel> _attachments = new();

        public AttachmentManagerView(IAttachmentService attachmentService, int relatedEntityId, string entityType)
        {
            InitializeComponent();
            _attachmentService = attachmentService;
            _relatedEntityId = relatedEntityId;
            _entityType = entityType;
            
            Loaded += AttachmentManagerView_Loaded;
        }

        private async void AttachmentManagerView_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadAttachmentsAsync();
        }

        private async Task LoadAttachmentsAsync()
        {
            try
            {
                StatusTextBlock.Text = "جاري تحميل المرفقات...";
                
                var attachments = await _attachmentService.GetByEntityAsync(_relatedEntityId, _entityType);
                _attachments = attachments.Select(a => new AttachmentViewModel(a)).ToList();
                
                AttachmentsDataGrid.ItemsSource = _attachments;
                StatusTextBlock.Text = $"تم تحميل {_attachments.Count} مرفق";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل المرفقات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "فشل في تحميل المرفقات";
            }
        }

        private void BrowseButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختر ملف للرفع",
                Filter = "جميع الملفات المدعومة|*.pdf;*.doc;*.docx;*.xls;*.xlsx;*.jpg;*.jpeg;*.png;*.gif;*.txt|" +
                        "ملفات PDF|*.pdf|" +
                        "مستندات Word|*.doc;*.docx|" +
                        "جداول Excel|*.xls;*.xlsx|" +
                        "الصور|*.jpg;*.jpeg;*.png;*.gif|" +
                        "ملفات نصية|*.txt|" +
                        "جميع الملفات|*.*",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == true)
            {
                _selectedFilePath = openFileDialog.FileName;
                FilePathTextBox.Text = Path.GetFileName(_selectedFilePath);
                UploadButton.IsEnabled = true;
            }
        }

        private async void UploadButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_selectedFilePath) || !File.Exists(_selectedFilePath))
            {
                MessageBox.Show("يرجى اختيار ملف صالح للرفع", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                ShowLoading(true, "جاري رفع الملف...");
                UploadProgressBar.Visibility = Visibility.Visible;
                UploadProgressBar.IsIndeterminate = true;

                var fileInfo = new FileInfo(_selectedFilePath);
                var fileName = fileInfo.Name;
                var fileSize = fileInfo.Length;

                // Check file size (max 10MB)
                if (fileSize > 10 * 1024 * 1024)
                {
                    MessageBox.Show("حجم الملف كبير جداً. الحد الأقصى المسموح 10 ميجابايت", "تحذير", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var fileBytes = await File.ReadAllBytesAsync(_selectedFilePath);

                var attachment = new Attachment
                {
                    FileName = fileName,
                    FileSize = fileSize,
                    FileData = fileBytes,
                    ContentType = GetContentType(fileName),
                    EntityId = _relatedEntityId,
                    EntityType = _entityType,
                    UploadedByUserId = SessionManager.CurrentUserId,
                    UploadedDate = DateTime.Now
                };

                await _attachmentService.CreateAsync(attachment);

                MessageBox.Show("تم رفع الملف بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);

                // Clear selection and reload
                _selectedFilePath = null;
                FilePathTextBox.Text = "اختر ملف للرفع...";
                UploadButton.IsEnabled = false;
                
                await LoadAttachmentsAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء رفع الملف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ShowLoading(false);
                UploadProgressBar.Visibility = Visibility.Collapsed;
            }
        }

        private async void ViewButton_Click(object sender, RoutedEventArgs e)
        {
            var attachment = GetSelectedAttachment(sender);
            if (attachment == null) return;

            try
            {
                ShowLoading(true, "جاري فتح الملف...");

                var tempPath = Path.Combine(Path.GetTempPath(), attachment.FileName);
                await File.WriteAllBytesAsync(tempPath, attachment.FileData);

                Process.Start(new ProcessStartInfo
                {
                    FileName = tempPath,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح الملف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private async void DownloadButton_Click(object sender, RoutedEventArgs e)
        {
            var attachment = GetSelectedAttachment(sender);
            if (attachment == null) return;

            var saveFileDialog = new SaveFileDialog
            {
                FileName = attachment.FileName,
                Filter = "جميع الملفات|*.*"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    ShowLoading(true, "جاري تحميل الملف...");
                    
                    await File.WriteAllBytesAsync(saveFileDialog.FileName, attachment.FileData);
                    
                    MessageBox.Show("تم تحميل الملف بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء تحميل الملف: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    ShowLoading(false);
                }
            }
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            var attachmentViewModel = GetSelectedAttachmentViewModel(sender);
            if (attachmentViewModel == null) return;

            var result = MessageBox.Show(
                $"هل تريد حذف المرفق: {attachmentViewModel.FileName}؟\n\nهذا الإجراء لا يمكن التراجع عنه.", 
                "تأكيد الحذف", 
                MessageBoxButton.YesNo, 
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    ShowLoading(true, "جاري الحذف...");
                    
                    var success = await _attachmentService.DeleteAsync(attachmentViewModel.AttachmentId);
                    if (success)
                    {
                        await LoadAttachmentsAsync();
                        StatusTextBlock.Text = "تم حذف المرفق بنجاح";
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف المرفق", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء حذف المرفق: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    ShowLoading(false);
                }
            }
        }

        private Attachment? GetSelectedAttachment(object sender)
        {
            if (sender is Button button && button.DataContext is AttachmentViewModel viewModel)
            {
                return viewModel.Attachment;
            }
            return null;
        }

        private AttachmentViewModel? GetSelectedAttachmentViewModel(object sender)
        {
            if (sender is Button button && button.DataContext is AttachmentViewModel viewModel)
            {
                return viewModel;
            }
            return null;
        }

        private void ShowLoading(bool show, string message = "جاري المعالجة...")
        {
            LoadingBorder.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            LoadingTextBlock.Text = message;
        }

        private string GetContentType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".pdf" => "application/pdf",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".xls" => "application/vnd.ms-excel",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".txt" => "text/plain",
                _ => "application/octet-stream"
            };
        }
    }

    public class AttachmentViewModel
    {
        public Attachment Attachment { get; }

        public AttachmentViewModel(Attachment attachment)
        {
            Attachment = attachment;
        }

        public int AttachmentId => Attachment.AttachmentId;
        public string FileName => Attachment.FileName;
        public string FileSizeFormatted => FormatFileSize(Attachment.FileSize);
        public DateTime UploadedDate => Attachment.UploadedDate;
        public string FileTypeIcon => GetFileTypeIcon(Attachment.FileName);

        private string FormatFileSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
            return $"{bytes / (1024 * 1024):F1} MB";
        }

        private string GetFileTypeIcon(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".pdf" => "📄",
                ".doc" or ".docx" => "📝",
                ".xls" or ".xlsx" => "📊",
                ".jpg" or ".jpeg" or ".png" or ".gif" => "🖼️",
                ".txt" => "📃",
                _ => "📎"
            };
        }
    }
}
