using DocFlowSystem.Models;

namespace DocFlowSystem.Services.Interfaces
{
    public interface IAttachmentService
    {
        Task<IEnumerable<Attachment>> GetAllAsync();
        Task<Attachment?> GetByIdAsync(int id);
        Task<Attachment> CreateAsync(Attachment attachment);
        Task<Attachment> UpdateAsync(Attachment attachment);
        Task<bool> DeleteAsync(int id);
        Task<IEnumerable<Attachment>> GetByEntityAsync(int entityId, string entityType);
        Task<bool> ExistsAsync(int id);
        Task<long> GetTotalSizeByEntityAsync(int entityId, string entityType);
        Task<int> GetCountByEntityAsync(int entityId, string entityType);
    }
}
