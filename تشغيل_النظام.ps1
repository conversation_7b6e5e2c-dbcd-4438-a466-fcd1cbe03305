# نظام إدارة البريد الحكومي - DocFlow System
# PowerShell Script for Advanced Users

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    نظام إدارة البريد الحكومي" -ForegroundColor Yellow
Write-Host "    DocFlow Management System" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if .NET is installed
Write-Host "🔍 فحص متطلبات النظام..." -ForegroundColor Blue

try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET مثبت - الإصدار: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ خطأ: .NET غير مثبت" -ForegroundColor Red
    Write-Host "يرجى تثبيت .NET 8.0 من: https://dotnet.microsoft.com/download" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# Check project files
if (-not (Test-Path "DocFlowSystem\DocFlowSystem.csproj")) {
    Write-Host "❌ خطأ: ملفات المشروع غير موجودة" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "✅ ملفات المشروع موجودة" -ForegroundColor Green

# Navigate to project directory
Set-Location "DocFlowSystem"

Write-Host ""
Write-Host "🔧 تحضير النظام..." -ForegroundColor Blue

# Restore packages
Write-Host "📦 تحميل الحزم المطلوبة..." -ForegroundColor Yellow
try {
    dotnet restore --verbosity quiet
    Write-Host "✅ تم تحميل الحزم بنجاح" -ForegroundColor Green
} catch {
    Write-Host "❌ فشل في تحميل الحزم" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# Build project
Write-Host "🔨 بناء المشروع..." -ForegroundColor Yellow
try {
    dotnet build --configuration Release --verbosity quiet
    Write-Host "✅ تم بناء المشروع بنجاح" -ForegroundColor Green
} catch {
    Write-Host "❌ فشل في بناء المشروع" -ForegroundColor Red
    dotnet build --configuration Release
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# Update database
Write-Host "🗄️ فحص قاعدة البيانات..." -ForegroundColor Yellow
try {
    dotnet ef database update --verbosity quiet
    Write-Host "✅ قاعدة البيانات جاهزة" -ForegroundColor Green
} catch {
    Write-Host "⚠️ تحذير: مشكلة في قاعدة البيانات" -ForegroundColor Yellow
    dotnet ef database update
}

Write-Host ""
Write-Host "🎉 النظام جاهز للتشغيل!" -ForegroundColor Green
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "           معلومات تسجيل الدخول" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "👤 اسم المستخدم: admin" -ForegroundColor White
Write-Host "🔑 كلمة المرور: admin123" -ForegroundColor White
Write-Host "🎯 الدور: مدير النظام" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "🚀 تشغيل النظام..." -ForegroundColor Blue
Write-Host ""

# Run the application
try {
    dotnet run --configuration Release
} catch {
    Write-Host "❌ خطأ في تشغيل النظام" -ForegroundColor Red
}

Write-Host ""
Write-Host "📝 تم إغلاق النظام" -ForegroundColor Yellow
Write-Host "شكراً لاستخدام نظام إدارة البريد الحكومي" -ForegroundColor Green
Write-Host ""
Read-Host "اضغط Enter للخروج"
