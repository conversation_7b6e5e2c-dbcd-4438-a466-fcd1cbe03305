<Window x:Class="DocFlowSystem.Views.Login.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تسجيل الدخول - نظام إدارة البريد الحكومي"
        Height="700" Width="550"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        AllowsTransparency="True"
        WindowStyle="None"
        Background="Transparent"
        Focusable="True"
        KeyboardNavigation.TabNavigation="Cycle">

    <Window.Resources>
        <!-- Windows 11 Button Style -->
        <Style x:Key="Win11Button" TargetType="Button">
            <Setter Property="Background" Value="#0067C0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="24,14"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}"
                                CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#1A73E8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#1557B0"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Background" Value="#E0E0E0"/>
                                <Setter Property="Foreground" Value="#9E9E9E"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Windows 11 TextBox Style -->
        <Style x:Key="Win11TextBox" TargetType="TextBox">
            <Setter Property="Background" Value="#FAFAFA"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="IsEnabled" Value="True"/>
            <Setter Property="IsTabStop" Value="True"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="#0067C0"/>
                                <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                                <Setter TargetName="border" Property="Background" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="#BDBDBD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Windows 11 PasswordBox Style -->
        <Style x:Key="Win11PasswordBox" TargetType="PasswordBox">
            <Setter Property="Background" Value="#FAFAFA"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="IsEnabled" Value="True"/>
            <Setter Property="IsTabStop" Value="True"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="PasswordBox">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="#0067C0"/>
                                <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                                <Setter TargetName="border" Property="Background" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="#BDBDBD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Close Button Style -->
        <Style x:Key="CloseButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#666666"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="46"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontFamily" Value="Segoe MDL2 Assets"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#E81123"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- Main Container with Windows 11 Acrylic Background -->
    <Border Background="#F3F3F3" CornerRadius="12">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="8" Opacity="0.15" BlurRadius="20"/>
        </Border.Effect>
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Title Bar -->
            <Grid Grid.Row="0" Height="32" Background="Transparent" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                          Text="تسجيل الدخول" 
                          FontSize="12" 
                          FontFamily="Segoe UI"
                          Foreground="#666666"
                          VerticalAlignment="Center" 
                          Margin="16,0,0,0"/>
                
                <Button Grid.Column="1" 
                       x:Name="CloseButton"
                       Style="{StaticResource CloseButton}"
                       Content="&#xE8BB;"
                       Click="CloseButton_Click"/>
            </Grid>

            <!-- Main Content -->
            <Grid Grid.Row="1" Margin="40,20,40,40">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Logo and Title -->
                <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,20,0,40">
                    <!-- Logo placeholder -->
                    <Border Width="80" Height="80" 
                           Background="#0067C0" 
                           CornerRadius="40" 
                           Margin="0,0,0,20">
                        <TextBlock Text="📧" 
                                  FontSize="36" 
                                  HorizontalAlignment="Center" 
                                  VerticalAlignment="Center"
                                  Foreground="White"/>
                    </Border>
                    
                    <TextBlock Text="نظام إدارة البريد الحكومي" 
                              FontSize="24" 
                              FontWeight="SemiBold"
                              FontFamily="Segoe UI"
                              Foreground="#1F1F1F" 
                              HorizontalAlignment="Center"
                              Margin="0,0,0,8"/>
                    
                    <TextBlock Text="تسجيل الدخول إلى حسابك" 
                              FontSize="14" 
                              FontFamily="Segoe UI"
                              Foreground="#666666" 
                              HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Login Form -->
                <StackPanel Grid.Row="2" VerticalAlignment="Center" MaxWidth="320">
                    <!-- Username Field -->
                    <TextBlock Text="اسم المستخدم" 
                              FontSize="14" 
                              FontWeight="Medium"
                              FontFamily="Segoe UI"
                              Foreground="#1F1F1F" 
                              Margin="0,0,0,8"/>
                    
                    <TextBox x:Name="UsernameTextBox"
                            Style="{StaticResource Win11TextBox}"
                            Height="44"
                            Margin="0,0,0,24"
                            TabIndex="1"
                            IsTabStop="True"/>

                    <!-- Password Field -->
                    <TextBlock Text="كلمة المرور" 
                              FontSize="14" 
                              FontWeight="Medium"
                              FontFamily="Segoe UI"
                              Foreground="#1F1F1F" 
                              Margin="0,0,0,8"/>
                    
                    <PasswordBox x:Name="PasswordBox"
                                Style="{StaticResource Win11PasswordBox}"
                                Height="44"
                                Margin="0,0,0,24"
                                TabIndex="2"
                                IsTabStop="True"/>

                    <!-- Error Message -->
                    <TextBlock x:Name="ErrorMessageTextBlock" 
                              Text="" 
                              Foreground="#D13438" 
                              FontSize="12" 
                              FontFamily="Segoe UI"
                              HorizontalAlignment="Center" 
                              Margin="0,0,0,24"
                              Visibility="Collapsed"/>

                    <!-- Login Button -->
                    <Button x:Name="LoginButton" 
                           Content="تسجيل الدخول" 
                           Style="{StaticResource Win11Button}"
                           Height="44"
                           Click="LoginButton_Click"
                           IsDefault="True"
                           Margin="0,0,0,20"/>

                    <!-- Loading Indicator -->
                    <StackPanel x:Name="LoadingPanel" 
                               Orientation="Horizontal" 
                               HorizontalAlignment="Center" 
                               Visibility="Collapsed">
                        <ProgressBar Width="20" Height="20" IsIndeterminate="True" Margin="0,0,10,0"/>
                        <TextBlock Text="جاري تسجيل الدخول..." 
                                  FontSize="12" 
                                  FontFamily="Segoe UI"
                                  Foreground="#666666"/>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</Window>
