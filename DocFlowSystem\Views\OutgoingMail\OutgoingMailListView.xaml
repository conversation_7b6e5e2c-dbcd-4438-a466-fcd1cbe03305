<UserControl x:Class="DocFlowSystem.Views.OutgoingMail.OutgoingMailListView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             FlowDirection="RightToLeft"
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <UserControl.Resources>
        <Style x:Key="HeaderTextBlock" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>
        
        <Style x:Key="SearchButton" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
        
        <Style x:Key="ActionButton" TargetType="Button">
            <Setter Property="Margin" Value="5,0"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="📤 قائمة البريد الصادر" Style="{StaticResource HeaderTextBlock}"/>

        <!-- Search Panel -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
            </Border.Effect>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- First Row -->
                <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,10">
                    <TextBlock Text="البحث في الموضوع أو الرقم:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="SearchTextBox" Padding="8" Height="35"/>
                </StackPanel>

                <StackPanel Grid.Row="0" Grid.Column="1" Margin="10,0,10,10">
                    <TextBlock Text="الجهة المستقبلة:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="RecipientEntityTextBox" Padding="8" Height="35"/>
                </StackPanel>

                <StackPanel Grid.Row="0" Grid.Column="2" Margin="10,0,0,10">
                    <TextBlock Text="الحالة:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="StatusComboBox" Height="35" Padding="8">
                        <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                        <ComboBoxItem Content="مسودة"/>
                        <ComboBoxItem Content="مرسل"/>
                        <ComboBoxItem Content="مؤكد الاستلام"/>
                        <ComboBoxItem Content="مؤرشف"/>
                    </ComboBox>
                </StackPanel>

                <!-- Second Row -->
                <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="من تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <DatePicker x:Name="FromDatePicker" Height="35"/>
                </StackPanel>

                <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,10,0">
                    <TextBlock Text="إلى تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <DatePicker x:Name="ToDatePicker" Height="35"/>
                </StackPanel>

                <StackPanel Grid.Row="1" Grid.Column="2" Margin="10,0,0,0">
                    <TextBlock Text="الأولوية:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="PriorityComboBox" Height="35" Padding="8">
                        <ComboBoxItem Content="جميع الأولويات" IsSelected="True"/>
                        <ComboBoxItem Content="عالي"/>
                        <ComboBoxItem Content="متوسط"/>
                        <ComboBoxItem Content="منخفض"/>
                    </ComboBox>
                </StackPanel>

                <!-- Search Buttons -->
                <StackPanel Grid.Row="0" Grid.RowSpan="2" Grid.Column="3" 
                           VerticalAlignment="Bottom" Margin="20,0,0,0">
                    <Button x:Name="SearchButton" Content="🔍 بحث" 
                           Style="{StaticResource SearchButton}" 
                           Click="SearchButton_Click" Margin="0,0,0,10"/>
                    <Button x:Name="ClearButton" Content="🗑️ مسح" 
                           Background="#FF5722" Foreground="White" 
                           BorderThickness="0" Padding="15,8" 
                           Click="ClearButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Data Grid -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" Padding="10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
            </Border.Effect>
            
            <DataGrid x:Name="OutgoingMailDataGrid" 
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     SelectionMode="Single"
                     AlternatingRowBackground="#F9F9F9"
                     RowHeight="40">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الرقم الصادر" Binding="{Binding OutgoingNumber}" Width="120"/>
                    <DataGridTextColumn Header="الجهة المستقبلة" Binding="{Binding RecipientEntity}" Width="200"/>
                    <DataGridTextColumn Header="الموضوع" Binding="{Binding Subject}" Width="*"/>
                    <DataGridTextColumn Header="تاريخ الإرسال" Binding="{Binding SentDate, StringFormat=yyyy/MM/dd}" Width="120"/>
                    <DataGridTextColumn Header="الأولوية" Binding="{Binding Priority}" Width="80"/>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                    <DataGridTextColumn Header="البريد المرتبط" Binding="{Binding RelatedIncomingMail.IncomingNumber}" Width="120"/>
                    
                    <DataGridTemplateColumn Header="الإجراءات" Width="200">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Content="👁️" ToolTip="عرض" 
                                           Style="{StaticResource ActionButton}"
                                           Background="#4CAF50" Foreground="White"
                                           Click="ViewButton_Click"/>
                                    <Button Content="✏️" ToolTip="تعديل" 
                                           Style="{StaticResource ActionButton}"
                                           Background="#FF9800" Foreground="White"
                                           Click="EditButton_Click"/>
                                    <Button Content="🗑️" ToolTip="حذف" 
                                           Style="{StaticResource ActionButton}"
                                           Background="#F44336" Foreground="White"
                                           Click="DeleteButton_Click"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Action Panel -->
        <Border Grid.Row="3" Background="White" CornerRadius="8" Padding="15" Margin="0,20,0,0">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
            </Border.Effect>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                <Button x:Name="AddNewButton" Content="➕ إضافة بريد صادر جديد" 
                       Background="#4CAF50" Foreground="White" 
                       BorderThickness="0" Padding="20,10" 
                       FontWeight="Bold" Margin="0,0,20,0"
                       Click="AddNewButton_Click"/>
                
                <Button x:Name="RefreshButton" Content="🔄 تحديث" 
                       Background="#607D8B" Foreground="White" 
                       BorderThickness="0" Padding="15,10" 
                       Click="RefreshButton_Click" Margin="0,0,20,0"/>
                
                <TextBlock x:Name="StatusTextBlock" Text="جاهز" 
                          VerticalAlignment="Center" 
                          FontStyle="Italic" 
                          Foreground="Gray"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
