using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DocFlowSystem.Services.Interfaces;
using DocFlowSystem.Models;
using DocFlowSystem.Helpers;
using Microsoft.Extensions.DependencyInjection;

namespace DocFlowSystem.Views.OutgoingMail
{
    public partial class OutgoingMailListView : UserControl
    {
        private readonly IOutgoingMailService _outgoingMailService;
        private readonly IServiceProvider _serviceProvider;
        private List<Models.OutgoingMail> _allOutgoingMails = new();

        public OutgoingMailListView(IOutgoingMailService outgoingMailService, IServiceProvider serviceProvider)
        {
            InitializeComponent();
            _outgoingMailService = outgoingMailService;
            _serviceProvider = serviceProvider;
            Loaded += OutgoingMailListView_Loaded;
        }

        private async void OutgoingMailListView_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private async Task LoadDataAsync()
        {
            try
            {
                StatusTextBlock.Text = "جاري تحميل البيانات...";
                
                _allOutgoingMails = (await _outgoingMailService.GetAllAsync()).ToList();
                OutgoingMailDataGrid.ItemsSource = _allOutgoingMails;
                
                StatusTextBlock.Text = $"تم تحميل {_allOutgoingMails.Count} عنصر";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "فشل في تحميل البيانات";
            }
        }

        private async void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusTextBlock.Text = "جاري البحث...";

                var searchTerm = SearchTextBox.Text?.Trim();
                var recipientEntity = RecipientEntityTextBox.Text?.Trim();
                var status = GetSelectedComboBoxValue(StatusComboBox);
                var priority = GetSelectedComboBoxValue(PriorityComboBox);
                var fromDate = FromDatePicker.SelectedDate;
                var toDate = ToDatePicker.SelectedDate;

                var results = await _outgoingMailService.SearchAsync(
                    searchTerm, recipientEntity, fromDate, toDate, status, priority);

                OutgoingMailDataGrid.ItemsSource = results.ToList();
                StatusTextBlock.Text = $"تم العثور على {results.Count()} نتيجة";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "فشل في البحث";
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Clear();
            RecipientEntityTextBox.Clear();
            StatusComboBox.SelectedIndex = 0;
            PriorityComboBox.SelectedIndex = 0;
            FromDatePicker.SelectedDate = null;
            ToDatePicker.SelectedDate = null;
            
            OutgoingMailDataGrid.ItemsSource = _allOutgoingMails;
            StatusTextBlock.Text = $"تم عرض جميع العناصر ({_allOutgoingMails.Count})";
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private void AddNewButton_Click(object sender, RoutedEventArgs e)
        {
            if (!SessionManager.CanCreateMail())
            {
                MessageBox.Show("ليس لديك صلاحية لإضافة بريد صادر", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var addOutgoingMailView = new AddOutgoingMailView(_outgoingMailService, _serviceProvider);
                
                // Handle save event to refresh the list
                addOutgoingMailView.OutgoingMailSaved += async (s, args) => await LoadDataAsync();
                
                // Navigate to add view
                var parentFrame = FindParent<Frame>(this);
                if (parentFrame != null)
                {
                    parentFrame.Content = addOutgoingMailView;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح نافذة الإضافة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedMail = GetSelectedOutgoingMail(sender);
            if (selectedMail == null) return;

            // TODO: Navigate to view outgoing mail details
            MessageBox.Show($"عرض تفاصيل البريد الصادر: {selectedMail.Subject}", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (!SessionManager.CanEditMail())
            {
                MessageBox.Show("ليس لديك صلاحية لتعديل البريد الصادر", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var selectedMail = GetSelectedOutgoingMail(sender);
            if (selectedMail == null) return;

            // TODO: Navigate to edit outgoing mail view
            MessageBox.Show($"تعديل البريد الصادر: {selectedMail.Subject}", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (!SessionManager.CanDeleteMail())
            {
                MessageBox.Show("ليس لديك صلاحية لحذف البريد الصادر", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var selectedMail = GetSelectedOutgoingMail(sender);
            if (selectedMail == null) return;

            var result = MessageBox.Show(
                $"هل تريد حذف البريد الصادر: {selectedMail.Subject}؟\n\nهذا الإجراء لا يمكن التراجع عنه.", 
                "تأكيد الحذف", 
                MessageBoxButton.YesNo, 
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    StatusTextBlock.Text = "جاري الحذف...";
                    
                    var success = await _outgoingMailService.DeleteAsync(selectedMail.OutgoingMailId);
                    if (success)
                    {
                        await LoadDataAsync();
                        StatusTextBlock.Text = "تم الحذف بنجاح";
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف البريد الصادر", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        StatusTextBlock.Text = "فشل في الحذف";
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء الحذف: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    StatusTextBlock.Text = "فشل في الحذف";
                }
            }
        }

        private Models.OutgoingMail? GetSelectedOutgoingMail(object sender)
        {
            if (sender is Button button && button.DataContext is Models.OutgoingMail outgoingMail)
            {
                return outgoingMail;
            }
            return null;
        }

        private string? GetSelectedComboBoxValue(ComboBox comboBox)
        {
            if (comboBox.SelectedItem is ComboBoxItem item && item.Content.ToString() != $"جميع {(comboBox == StatusComboBox ? "الحالات" : "الأولويات")}")
            {
                return item.Content.ToString();
            }
            return null;
        }

        private T? FindParent<T>(DependencyObject child) where T : DependencyObject
        {
            var parentObject = VisualTreeHelper.GetParent(child);
            if (parentObject == null) return null;
            
            if (parentObject is T parent)
                return parent;
            
            return FindParent<T>(parentObject);
        }
    }
}
