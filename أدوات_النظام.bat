@echo off
chcp 65001 >nul
title أدوات صيانة نظام إدارة البريد

:MENU
cls
echo.
echo ========================================
echo      أدوات صيانة نظام إدارة البريد
echo ========================================
echo.
echo اختر العملية المطلوبة:
echo.
echo 1. 🚀 تشغيل النظام
echo 2. 🔧 إعادة بناء المشروع
echo 3. 🗄️ إعادة إنشاء قاعدة البيانات
echo 4. 📦 تحديث الحزم
echo 5. 🧹 تنظيف ملفات البناء
echo 6. 📊 عرض معلومات النظام
echo 7. 🔍 فحص حالة قاعدة البيانات
echo 8. 📋 عرض سجل الأخطاء
echo 9. ❌ خروج
echo.
set /p choice=أدخل رقم الخيار (1-9): 

if "%choice%"=="1" goto RUN_SYSTEM
if "%choice%"=="2" goto REBUILD_PROJECT
if "%choice%"=="3" goto RECREATE_DATABASE
if "%choice%"=="4" goto UPDATE_PACKAGES
if "%choice%"=="5" goto CLEAN_BUILD
if "%choice%"=="6" goto SYSTEM_INFO
if "%choice%"=="7" goto CHECK_DATABASE
if "%choice%"=="8" goto VIEW_LOGS
if "%choice%"=="9" goto EXIT

echo خيار غير صحيح، يرجى المحاولة مرة أخرى.
timeout /t 2 >nul
goto MENU

:RUN_SYSTEM
cls
echo 🚀 جاري تشغيل النظام...
cd DocFlowSystem
dotnet run
pause
goto MENU

:REBUILD_PROJECT
cls
echo 🔧 جاري إعادة بناء المشروع...
cd DocFlowSystem
echo 🧹 تنظيف الملفات القديمة...
dotnet clean
echo 📦 استعادة الحزم...
dotnet restore
echo 🔨 بناء المشروع...
dotnet build --configuration Release
echo ✅ تم إعادة بناء المشروع بنجاح
pause
goto MENU

:RECREATE_DATABASE
cls
echo 🗄️ جاري إعادة إنشاء قاعدة البيانات...
cd DocFlowSystem
echo ⚠️ تحذير: سيتم حذف جميع البيانات الموجودة!
set /p confirm=هل تريد المتابعة؟ (y/n): 
if /i not "%confirm%"=="y" goto MENU

echo 🗑️ حذف قاعدة البيانات الحالية...
if exist "DocFlowDB.db" del "DocFlowDB.db"
if exist "DocFlowDB.db-shm" del "DocFlowDB.db-shm"
if exist "DocFlowDB.db-wal" del "DocFlowDB.db-wal"

echo 🔄 إعادة إنشاء قاعدة البيانات...
dotnet ef database update
echo ✅ تم إعادة إنشاء قاعدة البيانات بنجاح
echo.
echo بيانات تسجيل الدخول:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
pause
goto MENU

:UPDATE_PACKAGES
cls
echo 📦 جاري تحديث الحزم...
cd DocFlowSystem
dotnet list package --outdated
echo.
echo 🔄 تحديث الحزم...
dotnet restore --force
echo ✅ تم تحديث الحزم بنجاح
pause
goto MENU

:CLEAN_BUILD
cls
echo 🧹 جاري تنظيف ملفات البناء...
cd DocFlowSystem
dotnet clean
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
echo ✅ تم تنظيف ملفات البناء بنجاح
pause
goto MENU

:SYSTEM_INFO
cls
echo 📊 معلومات النظام:
echo ==================
echo.
echo 🖥️ معلومات .NET:
dotnet --info
echo.
echo 📁 معلومات المشروع:
cd DocFlowSystem
if exist "DocFlowSystem.csproj" (
    echo ✅ ملف المشروع موجود
) else (
    echo ❌ ملف المشروع غير موجود
)

if exist "DocFlowDB.db" (
    echo ✅ قاعدة البيانات موجودة
    for %%A in ("DocFlowDB.db") do echo 📏 حجم قاعدة البيانات: %%~zA بايت
) else (
    echo ❌ قاعدة البيانات غير موجودة
)

echo.
pause
goto MENU

:CHECK_DATABASE
cls
echo 🔍 جاري فحص حالة قاعدة البيانات...
cd DocFlowSystem
if not exist "DocFlowDB.db" (
    echo ❌ قاعدة البيانات غير موجودة
    echo هل تريد إنشاؤها؟ (y/n)
    set /p create_db=
    if /i "%create_db%"=="y" (
        dotnet ef database update
        echo ✅ تم إنشاء قاعدة البيانات
    )
) else (
    echo ✅ قاعدة البيانات موجودة
    echo 📊 جاري فحص الجداول...
    
    :: يمكن إضافة فحص أكثر تفصيلاً هنا
    echo ✅ قاعدة البيانات تبدو سليمة
)
pause
goto MENU

:VIEW_LOGS
cls
echo 📋 عرض سجل الأخطاء:
echo ===================
echo.
cd DocFlowSystem
if exist "logs" (
    dir logs /b
    echo.
    echo أدخل اسم ملف السجل لعرضه (أو اضغط Enter للعودة):
    set /p logfile=
    if not "%logfile%"=="" (
        if exist "logs\%logfile%" (
            type "logs\%logfile%"
        ) else (
            echo ملف السجل غير موجود
        )
    )
) else (
    echo لا توجد ملفات سجل
)
pause
goto MENU

:EXIT
cls
echo.
echo شكراً لاستخدام أدوات صيانة نظام إدارة البريد
echo تم إنهاء البرنامج
echo.
exit /b 0
