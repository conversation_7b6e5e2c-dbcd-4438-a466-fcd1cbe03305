<Window x:Class="DocFlowSystem.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DocFlowSystem"
        mc:Ignorable="d"
        Title="نظام إدارة البريد الحكومي"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft"
        Background="#F9F9F9">

    <Window.Resources>
        <!-- Windows 11 Menu Button Style -->
        <Style x:Key="Win11MenuButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#323130"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="Margin" Value="4,1"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderThickness="0"
                                CornerRadius="6"
                                Margin="{TemplateBinding Margin}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#F3F2F1"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#EDEBE9"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Windows 11 Active Menu Button Style -->
        <Style x:Key="Win11ActiveMenuButton" TargetType="Button" BasedOn="{StaticResource Win11MenuButton}">
            <Setter Property="Background" Value="#E3F2FD"/>
            <Setter Property="Foreground" Value="#0067C0"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>

        <!-- Windows 11 Section Header Style -->
        <Style x:Key="Win11SectionHeader" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Foreground" Value="#8A8A8A"/>
            <Setter Property="Margin" Value="20,16,20,8"/>
        </Style>

        <!-- Windows 11 Separator Style -->
        <Style x:Key="Win11Separator" TargetType="Rectangle">
            <Setter Property="Fill" Value="#E1DFDD"/>
            <Setter Property="Height" Value="1"/>
            <Setter Property="Margin" Value="16,8"/>
            <Setter Property="Opacity" Value="0.8"/>
        </Style>

        <!-- Windows 11 Logout Button Style -->
        <Style x:Key="Win11LogoutButton" TargetType="Button" BasedOn="{StaticResource Win11MenuButton}">
            <Setter Property="Foreground" Value="#D13438"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderThickness="0"
                                CornerRadius="6"
                                Margin="{TemplateBinding Margin}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FDF2F2"/>
                                <Setter Property="Foreground" Value="#B91C1C"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#FEE2E2"/>
                                <Setter Property="Foreground" Value="#991B1B"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Windows 11 Title Bar Button Style -->
        <Style x:Key="Win11TitleBarButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#323130"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="46"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="FontSize" Value="10"/>
            <Setter Property="FontFamily" Value="Segoe MDL2 Assets"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#F3F2F1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Windows 11 Close Button Style -->
        <Style x:Key="Win11CloseButton" TargetType="Button" BasedOn="{StaticResource Win11TitleBarButton}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#E81123"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="280"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Windows 11 Style Sidebar -->
        <Border Grid.Column="0" Background="White">
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="0" ShadowDepth="1" Opacity="0.1" BlurRadius="8"/>
            </Border.Effect>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- App Header -->
                <Border Grid.Row="0" Background="#0067C0" Padding="20,16">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                            <TextBlock Text="📧" FontSize="20" Margin="0,0,12,0" VerticalAlignment="Center"/>
                            <TextBlock Text="نظام إدارة البريد" 
                                      FontSize="16" 
                                      FontWeight="SemiBold"
                                      FontFamily="Segoe UI"
                                      Foreground="White" 
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                        <TextBlock Text="الحكومي" 
                                  FontSize="14" 
                                  FontFamily="Segoe UI"
                                  Foreground="#B3D9FF" 
                                  Margin="32,0,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Navigation Menu -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="12,16">
                    <StackPanel>
                        <!-- Dashboard -->
                        <Button x:Name="DashboardButton"
                               Style="{StaticResource Win11ActiveMenuButton}"
                               Click="DashboardButton_Click"
                               Margin="4,4,4,8">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🏠" FontSize="16" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                <TextBlock Text="لوحة التحكم" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- Incoming Mail Section -->
                        <TextBlock Text="البريد الوارد" Style="{StaticResource Win11SectionHeader}"/>
                        
                        <Button x:Name="IncomingMailListButton"
                               Style="{StaticResource Win11MenuButton}"
                               Click="IncomingMailListButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📥" FontSize="16" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                <TextBlock Text="قائمة البريد الوارد" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="AddIncomingMailButton"
                               Style="{StaticResource Win11MenuButton}"
                               Click="AddIncomingMailButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="➕" FontSize="16" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                <TextBlock Text="إضافة بريد وارد" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- Outgoing Mail Section -->
                        <TextBlock Text="البريد الصادر" Style="{StaticResource Win11SectionHeader}"/>
                        
                        <Button x:Name="OutgoingMailListButton"
                               Style="{StaticResource Win11MenuButton}"
                               Click="OutgoingMailListButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📤" FontSize="16" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                <TextBlock Text="قائمة البريد الصادر" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="AddOutgoingMailButton"
                               Style="{StaticResource Win11MenuButton}"
                               Click="AddOutgoingMailButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📝" FontSize="16" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                <TextBlock Text="إضافة بريد صادر" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- Management Section -->
                        <TextBlock Text="الإدارة والتحكم" Style="{StaticResource Win11SectionHeader}"/>
                        
                        <Button x:Name="ReportsButton"
                               Style="{StaticResource Win11MenuButton}"
                               Click="ReportsButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📊" FontSize="16" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                <TextBlock Text="التقارير والإحصائيات" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="ArchiveButton"
                               Style="{StaticResource Win11MenuButton}"
                               Click="ArchiveButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🗃️" FontSize="16" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                <TextBlock Text="الأرشيف والحفظ" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- Admin Section -->
                        <StackPanel x:Name="AdminSection">
                            <TextBlock x:Name="AdminSectionHeader" Text="إدارة النظام" Style="{StaticResource Win11SectionHeader}"/>
                            
                            <Button x:Name="UserManagementButton"
                                   Style="{StaticResource Win11MenuButton}"
                                   Click="UserManagementButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="👥" FontSize="16" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="إدارة المستخدمين" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="SettingsButton"
                                   Style="{StaticResource Win11MenuButton}"
                                   Click="SettingsButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="⚙️" FontSize="16" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="إعدادات النظام" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>

                <!-- Logout Section -->
                <Border Grid.Row="2" Padding="12,8,12,16">
                    <StackPanel>
                        <Rectangle Style="{StaticResource Win11Separator}"/>
                        
                        <Button x:Name="LogoutButton"
                               Style="{StaticResource Win11LogoutButton}"
                               Click="LogoutButton_Click"
                               Margin="4,8,4,4">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🚪" FontSize="16" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                <TextBlock Text="تسجيل الخروج" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Windows 11 Style Title Bar -->
            <Border Grid.Row="0" Background="White" Height="48" BorderBrush="#E1DFDD" BorderThickness="0,0,0,1">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- User Info -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="24,0">
                        <Border Width="32" Height="32"
                               Background="#0067C0"
                               CornerRadius="16"
                               Margin="0,0,12,0">
                            <TextBlock Text="👤"
                                      FontSize="16"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"
                                      Foreground="White"/>
                        </Border>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock x:Name="UserInfoTextBlock"
                                      Text="مرحباً، المستخدم"
                                      FontSize="14"
                                      FontWeight="SemiBold"
                                      FontFamily="Segoe UI"
                                      Foreground="#323130"/>
                            <TextBlock Text="مدير النظام"
                                      FontSize="12"
                                      FontFamily="Segoe UI"
                                      Foreground="#8A8A8A"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Window Controls -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <Button x:Name="MinimizeButton"
                               Style="{StaticResource Win11TitleBarButton}"
                               Content="&#xE921;"
                               Click="MinimizeButton_Click"/>
                        <Button x:Name="MaximizeButton"
                               Style="{StaticResource Win11TitleBarButton}"
                               Content="&#xE922;"
                               Click="MaximizeButton_Click"/>
                        <Button x:Name="CloseButton"
                               Style="{StaticResource Win11CloseButton}"
                               Content="&#xE8BB;"
                               Click="CloseButton_Click"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Content Frame -->
            <Border Grid.Row="1" Background="#F9F9F9" Padding="24">
                <Frame x:Name="MainFrame"
                       NavigationUIVisibility="Hidden"
                       Background="Transparent"/>
            </Border>
        </Grid>
    </Grid>
</Window>
