<Project>
  <PropertyGroup>
    <AssemblyName>DocFlowSystem</AssemblyName>
    <IntermediateOutputPath>obj\Debug\</IntermediateOutputPath>
    <BaseIntermediateOutputPath>obj\</BaseIntermediateOutputPath>
    <MSBuildProjectExtensionsPath>C:\Users\<USER>\Desktop\DocFlow_2025\DocFlowSystem\obj\</MSBuildProjectExtensionsPath>
    <_TargetAssemblyProjectName>DocFlowSystem</_TargetAssemblyProjectName>
    <RootNamespace>DocFlowSystem</RootNamespace>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>
  <ItemGroup>
    <!-- Entity Framework Core -->
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.11" />
    <!-- PDF Generation -->
    <PackageReference Include="iTextSharp" Version="********" />
    <!-- Excel Generation -->
    <PackageReference Include="EPPlus" Version="6.2.10" />
    <!-- MVVM Toolkit -->
    <PackageReference Include="Microsoft.Toolkit.Mvvm" Version="7.1.2" />
    <!-- Password Hashing -->
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <!-- Configuration -->
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
    <!-- Dependency Injection -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
  </ItemGroup>
  <ItemGroup>
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.20\ref\net8.0\Accessibility.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\bcrypt.net-next\4.0.3\lib\net6.0\BCrypt.Net-Next.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\bouncycastle\1.8.9\lib\BouncyCastle.Crypto.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\epplus\6.2.10\lib\net7.0\EPPlus.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\epplus.interfaces\6.1.1\lib\net7.0\EPPlus.Interfaces.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\epplus.system.drawing\6.1.1\lib\net7.0\EPPlus.System.Drawing.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\humanizer.core\2.14.1\lib\net6.0\Humanizer.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\itextsharp\********\lib\itextsharp.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.bcl.asyncinterfaces\6.0.0\lib\netstandard2.1\Microsoft.Bcl.AsyncInterfaces.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp\4.5.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.CSharp.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.csharp.workspaces\4.5.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.CSharp.Workspaces.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.common\4.5.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.workspaces.common\4.5.0\lib\netcoreapp3.1\Microsoft.CodeAnalysis.Workspaces.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\Microsoft.CSharp.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.data.sqlite.core\8.0.11\lib\net8.0\Microsoft.Data.Sqlite.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.entityframeworkcore.abstractions\8.0.11\lib\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.entityframeworkcore.design\8.0.11\lib\net8.0\Microsoft.EntityFrameworkCore.Design.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.entityframeworkcore\8.0.11\lib\net8.0\Microsoft.EntityFrameworkCore.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.entityframeworkcore.relational\8.0.11\lib\net8.0\Microsoft.EntityFrameworkCore.Relational.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.entityframeworkcore.sqlite.core\8.0.11\lib\net8.0\Microsoft.EntityFrameworkCore.Sqlite.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.caching.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Caching.Abstractions.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.caching.memory\8.0.1\lib\net8.0\Microsoft.Extensions.Caching.Memory.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.binder\8.0.2\lib\net8.0\Microsoft.Extensions.Configuration.Binder.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.commandline\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.CommandLine.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.environmentvariables\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.EnvironmentVariables.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.fileextensions\8.0.1\lib\net8.0\Microsoft.Extensions.Configuration.FileExtensions.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.json\8.0.1\lib\net8.0\Microsoft.Extensions.Configuration.Json.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.usersecrets\8.0.1\lib\net8.0\Microsoft.Extensions.Configuration.UserSecrets.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection.abstractions\8.0.2\lib\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection\8.0.1\lib\net8.0\Microsoft.Extensions.DependencyInjection.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencymodel\8.0.2\lib\net8.0\Microsoft.Extensions.DependencyModel.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.diagnostics.abstractions\8.0.1\lib\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.diagnostics\8.0.1\lib\net8.0\Microsoft.Extensions.Diagnostics.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.FileProviders.Abstractions.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.physical\8.0.0\lib\net8.0\Microsoft.Extensions.FileProviders.Physical.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.filesystemglobbing\8.0.0\lib\net8.0\Microsoft.Extensions.FileSystemGlobbing.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.hosting.abstractions\8.0.1\lib\net8.0\Microsoft.Extensions.Hosting.Abstractions.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.hosting\8.0.1\lib\net8.0\Microsoft.Extensions.Hosting.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.2\lib\net8.0\Microsoft.Extensions.Logging.Abstractions.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.configuration\8.0.1\lib\net8.0\Microsoft.Extensions.Logging.Configuration.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.console\8.0.1\lib\net8.0\Microsoft.Extensions.Logging.Console.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.debug\8.0.1\lib\net8.0\Microsoft.Extensions.Logging.Debug.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging\8.0.1\lib\net8.0\Microsoft.Extensions.Logging.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.eventlog\8.0.1\lib\net8.0\Microsoft.Extensions.Logging.EventLog.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.eventsource\8.0.1\lib\net8.0\Microsoft.Extensions.Logging.EventSource.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.options.configurationextensions\8.0.0\lib\net8.0\Microsoft.Extensions.Options.ConfigurationExtensions.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.options\8.0.2\lib\net8.0\Microsoft.Extensions.Options.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.primitives\8.0.0\lib\net8.0\Microsoft.Extensions.Primitives.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.io.recyclablememorystream\2.2.1\lib\net5.0\Microsoft.IO.RecyclableMemoryStream.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\microsoft.toolkit.mvvm\7.1.2\lib\net5.0\Microsoft.Toolkit.Mvvm.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\Microsoft.VisualBasic.Core.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\Microsoft.VisualBasic.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\Microsoft.Win32.Primitives.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.20\ref\net8.0\Microsoft.Win32.Registry.AccessControl.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\Microsoft.Win32.Registry.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.20\ref\net8.0\Microsoft.Win32.SystemEvents.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\mono.texttemplating\2.2.1\lib\netstandard2.0\Mono.TextTemplating.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\mscorlib.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\netstandard.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.20\ref\net8.0\PresentationCore.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.20\ref\net8.0\PresentationFramework.Aero.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.20\ref\net8.0\PresentationFramework.Aero2.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.20\ref\net8.0\PresentationFramework.AeroLite.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.20\ref\net8.0\PresentationFramework.Classic.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.20\ref\net8.0\PresentationFramework.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.20\ref\net8.0\PresentationFramework.Luna.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.20\ref\net8.0\PresentationFramework.Royale.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.20\ref\net8.0\PresentationUI.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.20\ref\net8.0\ReachFramework.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\sqlitepclraw.bundle_e_sqlite3\2.1.6\lib\netstandard2.0\SQLitePCLRaw.batteries_v2.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\sqlitepclraw.core\2.1.6\lib\netstandard2.0\SQLitePCLRaw.core.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\sqlitepclraw.provider.e_sqlite3\2.1.6\lib\net6.0-windows7.0\SQLitePCLRaw.provider.e_sqlite3.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.AppContext.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.Buffers.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.20\ref\net8.0\System.CodeDom.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.Collections.Concurrent.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.Collections.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.Collections.Immutable.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.Collections.NonGeneric.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.Collections.Specialized.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.ComponentModel.Annotations.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.ComponentModel.DataAnnotations.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.ComponentModel.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.ComponentModel.EventBasedAsync.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.ComponentModel.Primitives.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.ComponentModel.TypeConverter.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\system.composition.attributedmodel\6.0.0\lib\net6.0\System.Composition.AttributedModel.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\system.composition.convention\6.0.0\lib\net6.0\System.Composition.Convention.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\system.composition.hosting\6.0.0\lib\net6.0\System.Composition.Hosting.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\system.composition.runtime\6.0.0\lib\net6.0\System.Composition.Runtime.dll" />
    <ReferencePath Include="C:\Users\<USER>\.nuget\packages\system.composition.typedparts\6.0.0\lib\net6.0\System.Composition.TypedParts.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.WindowsDesktop.App.Ref\8.0.20\ref\net8.0\System.Configuration.ConfigurationManager.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.Configuration.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.Console.dll" />
    <ReferencePath Include="C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.20\ref\net8.0\System.Core.dll" />
    <ReferencePath Include="C:\Program F