<UserControl x:Class="DocFlowSystem.Views.Attachments.AttachmentManagerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             FlowDirection="RightToLeft"
             d:DesignHeight="500" d:DesignWidth="700">
    
    <UserControl.Resources>
        <Style x:Key="HeaderTextBlock" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>
        
        <Style x:Key="ActionButton" TargetType="Button">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="📎 إدارة المرفقات" Style="{StaticResource HeaderTextBlock}"/>

        <!-- Upload Panel -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
            </Border.Effect>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="إضافة مرفق جديد:" FontWeight="Bold" Margin="0,0,0,10"/>
                    <TextBox x:Name="FilePathTextBox" 
                            IsReadOnly="True" 
                            Background="#F5F5F5" 
                            Height="35" 
                            Padding="8"
                            Text="اختر ملف للرفع..."/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Bottom">
                    <Button x:Name="BrowseButton" Content="📁 استعراض" 
                           Style="{StaticResource ActionButton}"
                           Background="#2196F3" Foreground="White"
                           Click="BrowseButton_Click" Margin="10,0,0,0"/>
                    
                    <Button x:Name="UploadButton" Content="⬆️ رفع" 
                           Style="{StaticResource ActionButton}"
                           Background="#4CAF50" Foreground="White"
                           Click="UploadButton_Click" 
                           IsEnabled="False"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Attachments List -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" Padding="10">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
            </Border.Effect>
            
            <DataGrid x:Name="AttachmentsDataGrid" 
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     SelectionMode="Single"
                     AlternatingRowBackground="#F9F9F9"
                     RowHeight="40">
                
                <DataGrid.Columns>
                    <DataGridTemplateColumn Header="النوع" Width="60">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding FileTypeIcon}" 
                                          FontSize="20" 
                                          HorizontalAlignment="Center"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    
                    <DataGridTextColumn Header="اسم الملف" Binding="{Binding FileName}" Width="*"/>
                    <DataGridTextColumn Header="الحجم" Binding="{Binding FileSizeFormatted}" Width="100"/>
                    <DataGridTextColumn Header="تاريخ الرفع" Binding="{Binding UploadedDate, StringFormat=yyyy/MM/dd HH:mm}" Width="150"/>
                    <DataGridTextColumn Header="رفع بواسطة" Binding="{Binding UploadedByUser.FullName}" Width="120"/>
                    
                    <DataGridTemplateColumn Header="الإجراءات" Width="200">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Content="👁️" ToolTip="عرض" 
                                           Style="{StaticResource ActionButton}"
                                           Background="#4CAF50" Foreground="White"
                                           Click="ViewButton_Click"/>
                                    <Button Content="💾" ToolTip="تحميل" 
                                           Style="{StaticResource ActionButton}"
                                           Background="#2196F3" Foreground="White"
                                           Click="DownloadButton_Click"/>
                                    <Button Content="🗑️" ToolTip="حذف" 
                                           Style="{StaticResource ActionButton}"
                                           Background="#F44336" Foreground="White"
                                           Click="DeleteButton_Click"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- Status Panel -->
        <Border Grid.Row="3" Background="White" CornerRadius="8" Padding="15" Margin="0,20,0,0">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
            </Border.Effect>
            
            <StackPanel Orientation="Horizontal">
                <TextBlock x:Name="StatusTextBlock" Text="جاهز" 
                          VerticalAlignment="Center" 
                          FontStyle="Italic" 
                          Foreground="Gray"/>
                
                <ProgressBar x:Name="UploadProgressBar" 
                            Width="200" 
                            Height="20" 
                            Margin="20,0,0,0"
                            Visibility="Collapsed"/>
            </StackPanel>
        </Border>

        <!-- Loading Indicator -->
        <Border x:Name="LoadingBorder" 
               Grid.RowSpan="4"
               Background="#80000000" 
               Visibility="Collapsed">
            <Border HorizontalAlignment="Center" 
                   VerticalAlignment="Center"
                   Background="White" 
                   Padding="30" 
                   CornerRadius="10">
                <StackPanel>
                    <ProgressBar Width="200" Height="20" IsIndeterminate="True" Margin="0,0,0,15"/>
                    <TextBlock x:Name="LoadingTextBlock" 
                              Text="جاري المعالجة..." 
                              HorizontalAlignment="Center" 
                              FontWeight="Bold"/>
                </StackPanel>
            </Border>
        </Border>
    </Grid>
</UserControl>
