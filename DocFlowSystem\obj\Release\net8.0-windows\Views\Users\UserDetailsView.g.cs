﻿#pragma checksum "..\..\..\..\..\Views\Users\UserDetailsView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "CBC6BEB9342887BCD5C235C85CAEF80C6665D553"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DocFlowSystem.Views.Users {
    
    
    /// <summary>
    /// UserDetailsView
    /// </summary>
    public partial class UserDetailsView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 38 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BackButton;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTextBlock;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UsernameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FullNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EmailTextBlock;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PhoneTextBlock;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RoleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DepartmentTextBlock;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusBorder;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CreatedDateTextBlock;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastLoginTextBlock;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CreatedByTextBlock;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditButton;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetPasswordButton;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ToggleActiveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.9.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DocFlowSystem;component/views/users/userdetailsview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.9.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BackButton = ((System.Windows.Controls.Button)(target));
            
            #line 41 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
            this.BackButton.Click += new System.Windows.RoutedEventHandler(this.BackButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.HeaderTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.UsernameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.FullNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.EmailTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.PhoneTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.RoleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.DepartmentTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.StatusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 10:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.CreatedDateTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.LastLoginTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.CreatedByTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.EditButton = ((System.Windows.Controls.Button)(target));
            
            #line 134 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
            this.EditButton.Click += new System.Windows.RoutedEventHandler(this.EditButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.ResetPasswordButton = ((System.Windows.Controls.Button)(target));
            
            #line 140 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
            this.ResetPasswordButton.Click += new System.Windows.RoutedEventHandler(this.ResetPasswordButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.ToggleActiveButton = ((System.Windows.Controls.Button)(target));
            
            #line 146 "..\..\..\..\..\Views\Users\UserDetailsView.xaml"
            this.ToggleActiveButton.Click += new System.Windows.RoutedEventHandler(this.ToggleActiveButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

