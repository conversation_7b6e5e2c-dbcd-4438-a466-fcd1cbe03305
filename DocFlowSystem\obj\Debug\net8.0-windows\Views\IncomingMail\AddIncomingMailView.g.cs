﻿#pragma checksum "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "CF2E858E458CF296CB50FFDEB93F6ACA14890463"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DocFlowSystem.Views.IncomingMail {
    
    
    /// <summary>
    /// AddIncomingMailView
    /// </summary>
    public partial class AddIncomingMailView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 77 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IncomingNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SenderEntityTextBox;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SubjectTextBox;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ReceivedDatePicker;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PriorityComboBox;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AssignedToUserComboBox;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ContentTextBox;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveAndNewButton;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LoadingBorder;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.9.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DocFlowSystem;V1.0.0.0;component/views/incomingmail/addincomingmailview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.9.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.IncomingNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.SenderEntityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.SubjectTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.ReceivedDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 5:
            this.PriorityComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.AssignedToUserComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.ContentTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 156 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.SaveAndNewButton = ((System.Windows.Controls.Button)(target));
            
            #line 161 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
            this.SaveAndNewButton.Click += new System.Windows.RoutedEventHandler(this.SaveAndNewButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 165 "..\..\..\..\..\Views\IncomingMail\AddIncomingMailView.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.LoadingBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 14:
            this.LoadingTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

