using Microsoft.EntityFrameworkCore;
using DocFlowSystem.Models;

namespace DocFlowSystem.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<User> Users { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<IncomingMail> IncomingMails { get; set; }
        public DbSet<OutgoingMail> OutgoingMails { get; set; }
        public DbSet<Attachment> Attachments { get; set; }
        public DbSet<Log> Logs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure User entity
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasIndex(e => e.Username).IsUnique();
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");

                entity.HasOne(d => d.UserRole)
                    .WithMany(p => p.Users)
                    .HasForeignKey(d => d.UserRoleId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(e => e.CreatedIncomingMails)
                    .WithOne(e => e.CreatedByUser)
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(e => e.AssignedIncomingMails)
                    .WithOne(e => e.AssignedToUser)
                    .HasForeignKey(e => e.AssignedToUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasMany(e => e.CreatedOutgoingMails)
                    .WithOne(e => e.CreatedByUser)
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure UserRole entity
            modelBuilder.Entity<UserRole>(entity =>
            {
                entity.HasIndex(e => e.RoleName).IsUnique();
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            // Configure IncomingMail entity
            modelBuilder.Entity<IncomingMail>(entity =>
            {
                entity.HasIndex(e => e.IncomingNumber).IsUnique();
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.Priority).HasDefaultValue("متوسط");
                entity.Property(e => e.Status).HasDefaultValue("جديد");

                entity.HasIndex(e => e.ReceivedDate);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.SenderEntity);
            });

            // Configure OutgoingMail entity
            modelBuilder.Entity<OutgoingMail>(entity =>
            {
                entity.HasIndex(e => e.OutgoingNumber).IsUnique();
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.Priority).HasDefaultValue("متوسط");
                entity.Property(e => e.Status).HasDefaultValue("مسودة");

                entity.HasIndex(e => e.SentDate);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.RecipientEntity);

                entity.HasOne(d => d.RelatedIncomingMail)
                    .WithMany(p => p.RelatedOutgoingMails)
                    .HasForeignKey(d => d.RelatedIncomingMailId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure Attachment entity
            modelBuilder.Entity<Attachment>(entity =>
            {
                entity.Property(e => e.UploadedDate).HasDefaultValueSql("datetime('now')");

                entity.HasOne(d => d.IncomingMail)
                    .WithMany(p => p.Attachments)
                    .HasForeignKey(d => d.IncomingMailId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.OutgoingMail)
                    .WithMany(p => p.Attachments)
                    .HasForeignKey(d => d.OutgoingMailId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Ensure attachment belongs to either incoming or outgoing mail, not both
                entity.ToTable(t => t.HasCheckConstraint("CHK_Attachment_Mail",
                    "(IncomingMailId IS NOT NULL AND OutgoingMailId IS NULL) OR (IncomingMailId IS NULL AND OutgoingMailId IS NOT NULL)"));
            });

            // Configure Log entity
            modelBuilder.Entity<Log>(entity =>
            {
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
                entity.HasIndex(e => e.CreatedDate);
                entity.HasIndex(e => e.UserId);
                entity.HasIndex(e => new { e.TableName, e.RecordId });
            });

            // Seed default data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed UserRoles
            modelBuilder.Entity<UserRole>().HasData(
                new UserRole { UserRoleId = 1, RoleName = "مدير", Description = "مدير النظام - صلاحيات كاملة", CreatedDate = DateTime.Now },
                new UserRole { UserRoleId = 2, RoleName = "مستخدم", Description = "مستخدم عادي - صلاحيات محدودة", CreatedDate = DateTime.Now },
                new UserRole { UserRoleId = 3, RoleName = "أرشيف", Description = "مسؤول الأرشيف - صلاحيات الأرشفة والبحث", CreatedDate = DateTime.Now }
            );

            // Seed default admin user (Password: admin - will be hashed in service)
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    UserId = 1,
                    Username = "admin",
                    PasswordHash = "$2a$12$LQv3c1yqBWVHxkd0LQ4bLu.Pz5yqBWVHxkd0LQ4bLu.Pz5yqBWVHxkd0LQ", // BCrypt hash for "admin"
                    FullName = "مدير النظام", 
                    Email = "<EMAIL>", 
                    Department = "إدارة تقنية المعلومات", 
                    UserRoleId = 1,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                }
            );
        }
    }
}
