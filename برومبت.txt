أنت مطوّر محترف بلغة C# باستخدام WPF.  
قم ببناء تطبيق مكتبي متكامل لإدارة البريد الصادر والوارد اليومي داخل إدارة حكومية.  

🔹 البيئة التقنية المطلوبة:  
- اللغة: C#  
- الإطار: WPF (NET 6 أو أحدث)  
- قاعدة البيانات: SQL Server  
- ORM: ADO.NET أو Entity Framework Core  
- التقارير: إمكانية التصدير إلى PDF وExcel  

🔹 المميزات الأساسية:  
1. نظام الدخول والصلاحيات:  
   - شاشة تسجيل دخول (اسم مستخدم، كلمة مرور).  
   - صلاحيات حسب الدور (مدير – مستخدم – أرشيف).  

2. إدارة البريد الوارد:  
   - إضافة بريد وارد جديد (رقم وارد، الجهة المرسلة، الموضوع، التاريخ، الحالة، المرفقات).  
   - تتبع حالة البريد (جديد، قيد الإجراء، منجز).  
   - البحث والتصفية حسب الرقم، الموضوع، الجهة أو التاريخ.  

3. إدارة البريد الصادر:  
   - إضافة بريد صادر جديد (رقم صادر، الجهة الموجه إليها، الموضوع، التاريخ، المرفقات).  
   - ربط البريد الصادر ببريد وارد سابق (في حال الرد).  
   - البحث والتصفية.  

4. إدارة المرفقات:  
   - رفع واستعراض الملفات (PDF / Word / صور).  
   - حفظ مسارها داخل قاعدة البيانات.  

5. التقارير:  
   - توليد تقارير حسب الفترة الزمنية، الجهة المرسلة أو المستقبلة.  
   - تصدير التقارير إلى PDF وExcel.  

6. الأرشفة والسجلات:  
   - بحث متقدم في السجلات القديمة.  
   - سجل نشاط المستخدمين (إضافة – تعديل – حذف).  

7. النسخ الاحتياطي والاسترجاع:  
   - خاصية النسخ الاحتياطي لقاعدة البيانات.  
   - خاصية الاسترجاع عند الحاجة.  

🔹 المطلوب:  
- مشروع WPF كامل (يفضل استخدام نمط MVVM).  
- مخطط قاعدة بيانات SQL Server (جداول: Users, IncomingMail, OutgoingMail, Logs).  
- أمثلة على أوامر CRUD للبريد الوارد والصادر.  
- مشروع جاهز للتشغيل مع بيانات تجريبية.  