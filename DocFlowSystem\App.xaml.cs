﻿using System.Configuration;
using System.Data;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using DocFlowSystem.Data;
using DocFlowSystem.Data.Repositories;
using DocFlowSystem.Services.Interfaces;
using DocFlowSystem.Services.Implementations;
using DocFlowSystem.Views.Login;

namespace DocFlowSystem;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        try
        {
            _host = CreateHostBuilder().Build();
            await _host.StartAsync();

            // Initialize database
            var dbInitializer = _host.Services.GetRequiredService<DatabaseInitializerService>();
            await dbInitializer.InitializeAsync();

            // Show login window as the main window
            var loginWindow = _host.Services.GetRequiredService<LoginWindow>();
            this.MainWindow = loginWindow;
            loginWindow.Show();

            base.OnStartup(e);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء بدء التطبيق: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            this.Shutdown();
        }
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }

        base.OnExit(e);
    }

    private IHostBuilder CreateHostBuilder()
    {
        return Host.CreateDefaultBuilder()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            })
            .ConfigureServices((context, services) =>
            {
                // Database
                services.AddDbContext<ApplicationDbContext>(options =>
                    options.UseSqlite(context.Configuration.GetConnectionString("DefaultConnection")));

                // Repositories
                services.AddScoped<IUnitOfWork, UnitOfWork>();

                // Services
                services.AddScoped<IAuthenticationService, AuthenticationService>();
                services.AddScoped<IIncomingMailService, IncomingMailService>();
                services.AddScoped<IOutgoingMailService, OutgoingMailService>();
                services.AddScoped<IAttachmentService, AttachmentService>();
                services.AddScoped<IReportService, ReportService>();
                services.AddScoped<IUserService, UserService>();
                services.AddScoped<IUserRoleService, UserRoleService>();
                services.AddScoped<DatabaseInitializerService>();

                // Windows
                services.AddTransient<LoginWindow>();
                services.AddTransient<MainWindow>();
            });
    }
}

