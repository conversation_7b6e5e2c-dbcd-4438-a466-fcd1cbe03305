using System.Windows;
using System.Windows.Input;
using Microsoft.Extensions.DependencyInjection;
using DocFlowSystem.Services.Interfaces;

namespace DocFlowSystem.Views.Login
{
    public partial class LoginWindow : Window
    {
        private readonly IAuthenticationService _authenticationService;
        private readonly IServiceProvider _serviceProvider;

        public LoginWindow(IAuthenticationService authenticationService, IServiceProvider serviceProvider)
        {
            InitializeComponent();
            _authenticationService = authenticationService;
            _serviceProvider = serviceProvider;

            // Set default values for testing
            UsernameTextBox.Text = "admin";
            PasswordBox.Password = "admin";

            // Handle Enter key press
            KeyDown += LoginWindow_KeyDown;

            // Set focus to username textbox when window loads
            Loaded += (s, e) =>
            {
                UsernameTextBox.Focus();
                UsernameTextBox.SelectAll();
            };
        }

        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ButtonState == MouseButtonState.Pressed)
            {
                this.DragMove();
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            // Ask for confirmation before closing
            var result = MessageBox.Show("هل تريد إغلاق التطبيق؟", "تأكيد الإغلاق",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                Application.Current.Shutdown();
            }
        }

        private void LoginWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                LoginButton_Click(sender, e);
            }
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformLoginAsync();
        }

        private async Task PerformLoginAsync()
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
                {
                    ShowError("يرجى إدخال اسم المستخدم");
                    UsernameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(PasswordBox.Password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    PasswordBox.Focus();
                    return;
                }

                // Show loading
                ShowLoading(true);
                HideError();

                // Attempt login
                var user = await _authenticationService.LoginAsync(UsernameTextBox.Text.Trim(), PasswordBox.Password);

                if (user != null)
                {
                    // Login successful - open main window
                    var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();

                    // Set main window as the application's main window
                    Application.Current.MainWindow = mainWindow;
                    mainWindow.Show();

                    // Hide login window instead of closing to prevent app shutdown
                    this.Hide();
                }
                else
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                    PasswordBox.Clear();
                    PasswordBox.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}");
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void ShowError(string message)
        {
            ErrorMessageTextBlock.Text = message;
            ErrorMessageTextBlock.Visibility = Visibility.Visible;
        }

        private void HideError()
        {
            ErrorMessageTextBlock.Visibility = Visibility.Collapsed;
        }

        private void ShowLoading(bool show)
        {
            LoadingPanel.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            LoginButton.IsEnabled = !show;
            UsernameTextBox.IsEnabled = !show;
            PasswordBox.IsEnabled = !show;
        }

        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            // If main window is not visible, user is trying to close login window
            if (Application.Current.MainWindow == this ||
                (Application.Current.MainWindow != null && !Application.Current.MainWindow.IsVisible))
            {
                // Ask for confirmation before closing
                var result = MessageBox.Show("هل تريد إغلاق التطبيق؟", "تأكيد الإغلاق",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.No)
                {
                    e.Cancel = true;
                    return;
                }

                // Shutdown the application
                Application.Current.Shutdown();
            }

            base.OnClosing(e);
        }
    }
}
