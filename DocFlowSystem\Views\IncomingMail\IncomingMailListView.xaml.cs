using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DocFlowSystem.Services.Interfaces;
using DocFlowSystem.Models;
using DocFlowSystem.Helpers;
using Microsoft.Extensions.DependencyInjection;

namespace DocFlowSystem.Views.IncomingMail
{
    public partial class IncomingMailListView : UserControl
    {
        private readonly IIncomingMailService _incomingMailService;
        private readonly IServiceProvider _serviceProvider;
        private List<Models.IncomingMail> _allIncomingMails = new();

        public IncomingMailListView(IIncomingMailService incomingMailService, IServiceProvider serviceProvider)
        {
            InitializeComponent();
            _incomingMailService = incomingMailService;
            _serviceProvider = serviceProvider;
            Loaded += IncomingMailListView_Loaded;
        }

        private async void IncomingMailListView_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private async Task LoadDataAsync()
        {
            try
            {
                StatusTextBlock.Text = "جاري تحميل البيانات...";
                
                _allIncomingMails = (await _incomingMailService.GetAllAsync()).ToList();
                IncomingMailDataGrid.ItemsSource = _allIncomingMails;
                
                StatusTextBlock.Text = $"تم تحميل {_allIncomingMails.Count} عنصر";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "فشل في تحميل البيانات";
            }
        }

        private async void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusTextBlock.Text = "جاري البحث...";

                var searchTerm = SearchTextBox.Text?.Trim();
                var senderEntity = SenderEntityTextBox.Text?.Trim();
                var status = GetSelectedComboBoxValue(StatusComboBox);
                var priority = GetSelectedComboBoxValue(PriorityComboBox);
                var fromDate = FromDatePicker.SelectedDate;
                var toDate = ToDatePicker.SelectedDate;

                var results = await _incomingMailService.SearchAsync(
                    searchTerm, senderEntity, fromDate, toDate, status, priority);

                IncomingMailDataGrid.ItemsSource = results.ToList();
                StatusTextBlock.Text = $"تم العثور على {results.Count()} نتيجة";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "فشل في البحث";
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Clear();
            SenderEntityTextBox.Clear();
            StatusComboBox.SelectedIndex = 0;
            PriorityComboBox.SelectedIndex = 0;
            FromDatePicker.SelectedDate = null;
            ToDatePicker.SelectedDate = null;
            
            IncomingMailDataGrid.ItemsSource = _allIncomingMails;
            StatusTextBlock.Text = $"تم عرض جميع العناصر ({_allIncomingMails.Count})";
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private void AddNewButton_Click(object sender, RoutedEventArgs e)
        {
            if (!SessionManager.CanCreateMail())
            {
                MessageBox.Show("ليس لديك صلاحية لإضافة بريد وارد", "تحذير",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var addIncomingMailView = new AddIncomingMailView(_incomingMailService, _serviceProvider);

                // Handle save event to refresh the list
                addIncomingMailView.IncomingMailSaved += async (s, args) => await LoadDataAsync();

                // Navigate to add view (in a real app, you'd use proper navigation)
                var parentFrame = FindParent<Frame>(this);
                if (parentFrame != null)
                {
                    parentFrame.Content = addIncomingMailView;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح نافذة الإضافة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedMail = GetSelectedIncomingMail(sender);
            if (selectedMail == null) return;

            // TODO: Navigate to view incoming mail details
            MessageBox.Show($"عرض تفاصيل البريد الوارد: {selectedMail.Subject}", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (!SessionManager.CanEditMail())
            {
                MessageBox.Show("ليس لديك صلاحية لتعديل البريد الوارد", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var selectedMail = GetSelectedIncomingMail(sender);
            if (selectedMail == null) return;

            // TODO: Navigate to edit incoming mail view
            MessageBox.Show($"تعديل البريد الوارد: {selectedMail.Subject}", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (!SessionManager.CanDeleteMail())
            {
                MessageBox.Show("ليس لديك صلاحية لحذف البريد الوارد", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var selectedMail = GetSelectedIncomingMail(sender);
            if (selectedMail == null) return;

            var result = MessageBox.Show(
                $"هل تريد حذف البريد الوارد: {selectedMail.Subject}؟\n\nهذا الإجراء لا يمكن التراجع عنه.", 
                "تأكيد الحذف", 
                MessageBoxButton.YesNo, 
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    StatusTextBlock.Text = "جاري الحذف...";
                    
                    var success = await _incomingMailService.DeleteAsync(selectedMail.IncomingMailId);
                    if (success)
                    {
                        await LoadDataAsync();
                        StatusTextBlock.Text = "تم الحذف بنجاح";
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف البريد الوارد", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        StatusTextBlock.Text = "فشل في الحذف";
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء الحذف: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    StatusTextBlock.Text = "فشل في الحذف";
                }
            }
        }

        private Models.IncomingMail? GetSelectedIncomingMail(object sender)
        {
            if (sender is Button button && button.DataContext is Models.IncomingMail incomingMail)
            {
                return incomingMail;
            }
            return null;
        }

        private string? GetSelectedComboBoxValue(ComboBox comboBox)
        {
            if (comboBox.SelectedItem is ComboBoxItem item && item.Content.ToString() != $"جميع {(comboBox == StatusComboBox ? "الحالات" : "الأولويات")}")
            {
                return item.Content.ToString();
            }
            return null;
        }

        private T? FindParent<T>(DependencyObject child) where T : DependencyObject
        {
            var parentObject = VisualTreeHelper.GetParent(child);
            if (parentObject == null) return null;

            if (parentObject is T parent)
                return parent;

            return FindParent<T>(parentObject);
        }
    }
}
