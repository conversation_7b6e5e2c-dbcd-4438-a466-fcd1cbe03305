using System.Windows;
using System.Windows.Controls;
using DocFlowSystem.Services.Interfaces;
using DocFlowSystem.Models;
using DocFlowSystem.Helpers;
using Microsoft.Extensions.DependencyInjection;

namespace DocFlowSystem.Views.OutgoingMail
{
    public partial class AddOutgoingMailView : UserControl
    {
        private readonly IOutgoingMailService _outgoingMailService;
        private readonly IIncomingMailService _incomingMailService;
        private readonly IServiceProvider _serviceProvider;
        public event EventHandler? OutgoingMailSaved;

        public AddOutgoingMailView(IOutgoingMailService outgoingMailService, IServiceProvider serviceProvider)
        {
            InitializeComponent();
            _outgoingMailService = outgoingMailService;
            _incomingMailService = serviceProvider.GetRequiredService<IIncomingMailService>();
            _serviceProvider = serviceProvider;
            
            Loaded += AddOutgoingMailView_Loaded;
        }

        private async void AddOutgoingMailView_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadIncomingMailsAsync();
            await GenerateOutgoingNumberAsync();
        }

        private async Task LoadIncomingMailsAsync()
        {
            try
            {
                var incomingMails = await _incomingMailService.GetAllAsync();
                var incomingMailItems = incomingMails.Select(im => new
                {
                    IncomingMailId = im.IncomingMailId,
                    DisplayText = $"{im.IncomingNumber} - {im.Subject}"
                }).ToList();

                // Add empty option
                incomingMailItems.Insert(0, new { IncomingMailId = 0, DisplayText = "-- لا يوجد بريد وارد مرتبط --" });

                RelatedIncomingMailComboBox.ItemsSource = incomingMailItems;
                RelatedIncomingMailComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل قائمة البريد الوارد: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task GenerateOutgoingNumberAsync()
        {
            try
            {
                var outgoingNumber = await _outgoingMailService.GenerateOutgoingNumberAsync();
                OutgoingNumberTextBox.Text = outgoingNumber;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء الرقم الصادر: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            await SaveOutgoingMailAsync(false);
        }

        private async void SaveAndNewButton_Click(object sender, RoutedEventArgs e)
        {
            await SaveOutgoingMailAsync(true);
        }

        private async Task SaveOutgoingMailAsync(bool saveAndNew)
        {
            if (!ValidateForm()) return;

            try
            {
                ShowLoading(true, "جاري الحفظ...");

                var relatedIncomingMailId = RelatedIncomingMailComboBox.SelectedValue as int?;
                if (relatedIncomingMailId == 0) relatedIncomingMailId = null;

                var outgoingMail = new Models.OutgoingMail
                {
                    OutgoingNumber = OutgoingNumberTextBox.Text.Trim(),
                    RecipientEntity = RecipientEntityTextBox.Text.Trim(),
                    Subject = SubjectTextBox.Text.Trim(),
                    Content = ContentTextBox.Text?.Trim(),
                    SentDate = SentDatePicker.SelectedDate ?? DateTime.Now,
                    Priority = GetSelectedComboBoxValue(PriorityComboBox) ?? "متوسط",
                    Status = GetSelectedComboBoxValue(StatusComboBox) ?? "مسودة",
                    RelatedIncomingMailId = relatedIncomingMailId,
                    CreatedByUserId = SessionManager.CurrentUserId,
                    Notes = NotesTextBox.Text?.Trim()
                };

                var savedMail = await _outgoingMailService.CreateAsync(outgoingMail);

                MessageBox.Show("تم حفظ البريد الصادر بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);

                OutgoingMailSaved?.Invoke(this, EventArgs.Empty);

                if (saveAndNew)
                {
                    ClearForm();
                    await GenerateOutgoingNumberAsync();
                }
                else
                {
                    // Navigate back to list or close
                    OutgoingMailSaved?.Invoke(this, EventArgs.Empty);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البريد الصادر: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(RecipientEntityTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الجهة المستقبلة", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                RecipientEntityTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(SubjectTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الموضوع", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                SubjectTextBox.Focus();
                return false;
            }

            if (!SentDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ الإرسال", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                SentDatePicker.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            RecipientEntityTextBox.Clear();
            SubjectTextBox.Clear();
            ContentTextBox.Clear();
            NotesTextBox.Clear();
            SentDatePicker.SelectedDate = DateTime.Now;
            PriorityComboBox.SelectedIndex = 1; // متوسط
            StatusComboBox.SelectedIndex = 0; // مسودة
            RelatedIncomingMailComboBox.SelectedIndex = 0; // لا يوجد
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إلغاء إضافة البريد الصادر؟\n\nسيتم فقدان جميع البيانات المدخلة.", 
                "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                OutgoingMailSaved?.Invoke(this, EventArgs.Empty); // Navigate back
            }
        }

        private void ShowLoading(bool show, string message = "جاري التحميل...")
        {
            LoadingBorder.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            LoadingTextBlock.Text = message;
            
            // Disable form controls
            RecipientEntityTextBox.IsEnabled = !show;
            SubjectTextBox.IsEnabled = !show;
            ContentTextBox.IsEnabled = !show;
            NotesTextBox.IsEnabled = !show;
            SentDatePicker.IsEnabled = !show;
            PriorityComboBox.IsEnabled = !show;
            StatusComboBox.IsEnabled = !show;
            RelatedIncomingMailComboBox.IsEnabled = !show;
            SaveButton.IsEnabled = !show;
            SaveAndNewButton.IsEnabled = !show;
            CancelButton.IsEnabled = !show;
        }

        private string? GetSelectedComboBoxValue(ComboBox comboBox)
        {
            if (comboBox.SelectedItem is ComboBoxItem item)
            {
                return item.Content.ToString();
            }
            return null;
        }
    }
}
