@echo off
chcp 65001 >nul
title نظام إدارة البريد الحكومي - DocFlow System

echo.
echo ========================================
echo    نظام إدارة البريد الحكومي
echo    DocFlow Management System
echo ========================================
echo.

echo 🔍 جاري فحص متطلبات النظام...

:: Check if .NET is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: .NET غير مثبت على النظام
    echo يرجى تثبيت .NET 8.0 أو أحدث من الرابط التالي:
    echo https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)

echo ✅ .NET مثبت بنجاح

:: Check if project exists
if not exist "DocFlowSystem\DocFlowSystem.csproj" (
    echo ❌ خطأ: ملفات المشروع غير موجودة
    echo تأكد من وجود مجلد DocFlowSystem في نفس مكان هذا الملف
    echo.
    pause
    exit /b 1
)

echo ✅ ملفات المشروع موجودة

:: Navigate to project directory
cd DocFlowSystem

echo.
echo 🔧 جاري تحضير النظام...

:: Restore packages
echo 📦 جاري تحميل الحزم المطلوبة...
dotnet restore >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في تحميل الحزم المطلوبة
    echo جاري المحاولة مرة أخرى...
    dotnet restore
    if %errorlevel% neq 0 (
        echo ❌ فشل نهائي في تحميل الحزم
        pause
        exit /b 1
    )
)
echo ✅ تم تحميل الحزم بنجاح

:: Build project
echo 🔨 جاري بناء المشروع...
dotnet build --configuration Release --verbosity quiet >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    echo جاري عرض تفاصيل الخطأ...
    dotnet build --configuration Release
    pause
    exit /b 1
)
echo ✅ تم بناء المشروع بنجاح

:: Check and update database
echo 🗄️ جاري فحص قاعدة البيانات...
dotnet ef database update >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ تحذير: مشكلة في قاعدة البيانات، جاري المحاولة مرة أخرى...
    dotnet ef database update
)
echo ✅ قاعدة البيانات جاهزة

echo.
echo 🎉 النظام جاهز للتشغيل!
echo.
echo ========================================
echo           معلومات تسجيل الدخول
echo ========================================
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo 🎯 الدور: مدير النظام
echo ========================================
echo.

echo 🚀 جاري تشغيل النظام...
echo.
echo ملاحظة: سيتم فتح النظام في نافذة جديدة
echo لإغلاق النظام، أغلق النافذة أو اضغط Ctrl+C
echo.

:: Run the application
dotnet run --configuration Release

echo.
echo 📝 تم إغلاق النظام
echo شكراً لاستخدام نظام إدارة البريد الحكومي
echo.
pause
