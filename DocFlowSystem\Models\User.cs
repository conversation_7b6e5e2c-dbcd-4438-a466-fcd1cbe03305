using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DocFlowSystem.Models
{
    public class User
    {
        [Key]
        public int UserId { get; set; }

        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string PasswordHash { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Email { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(100)]
        public string? Department { get; set; }

        [Required]
        public int UserRoleId { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? UpdatedDate { get; set; }

        public DateTime? LastLoginDate { get; set; }

        public int? CreatedByUserId { get; set; }

        public int? UpdatedByUserId { get; set; }

        // Navigation Properties
        [ForeignKey("UserRoleId")]
        public virtual UserRole? UserRole { get; set; }

        public virtual ICollection<IncomingMail> CreatedIncomingMails { get; set; } = new List<IncomingMail>();
        public virtual ICollection<IncomingMail> AssignedIncomingMails { get; set; } = new List<IncomingMail>();
        public virtual ICollection<OutgoingMail> CreatedOutgoingMails { get; set; } = new List<OutgoingMail>();
        public virtual ICollection<Attachment> UploadedAttachments { get; set; } = new List<Attachment>();
        public virtual ICollection<Log> Logs { get; set; } = new List<Log>();
    }
}
