@echo off
chcp 65001 >nul
title القائمة الرئيسية - نظام إدارة البريد الحكومي

:MAIN_MENU
cls
echo.
echo ╔════════════════════════════════════════╗
echo ║        نظام إدارة البريد الحكومي        ║
echo ║      DocFlow Management System         ║
echo ╚════════════════════════════════════════╝
echo.
echo 🎯 اختر العملية المطلوبة:
echo.
echo ┌─────────────────────────────────────────┐
echo │  1. 🚀 تشغيل النظام                    │
echo │  2. 🔧 أدوات الصيانة والإعدادات        │
echo │  3. 💾 النسخ الاحتياطي                 │
echo │  4. 📥 استعادة النسخة الاحتياطية       │
echo │  5. 📋 معلومات النظام                  │
echo │  6. 📖 دليل الاستخدام                  │
echo │  7. ❌ خروج                            │
echo └─────────────────────────────────────────┘
echo.
set /p choice=أدخل رقم الخيار (1-7): 

if "%choice%"=="1" goto RUN_SYSTEM
if "%choice%"=="2" goto MAINTENANCE_TOOLS
if "%choice%"=="3" goto BACKUP
if "%choice%"=="4" goto RESTORE
if "%choice%"=="5" goto SYSTEM_INFO
if "%choice%"=="6" goto USER_GUIDE
if "%choice%"=="7" goto EXIT

echo ❌ خيار غير صحيح، يرجى المحاولة مرة أخرى.
timeout /t 2 >nul
goto MAIN_MENU

:RUN_SYSTEM
cls
echo 🚀 تشغيل نظام إدارة البريد...
echo.
call "تشغيل_نظام_إدارة_البريد.bat"
goto MAIN_MENU

:MAINTENANCE_TOOLS
cls
echo 🔧 فتح أدوات الصيانة...
echo.
call "أدوات_النظام.bat"
goto MAIN_MENU

:BACKUP
cls
echo 💾 إنشاء نسخة احتياطية...
echo.
call "نسخ_احتياطي.bat"
goto MAIN_MENU

:RESTORE
cls
echo 📥 استعادة النسخة الاحتياطية...
echo.
call "استعادة_النسخة_الاحتياطية.bat"
goto MAIN_MENU

:SYSTEM_INFO
cls
echo ╔════════════════════════════════════════╗
echo ║              معلومات النظام            ║
echo ╚════════════════════════════════════════╝
echo.
echo 📋 معلومات عامة:
echo ================
echo 🏷️  اسم النظام: نظام إدارة البريد الحكومي
echo 🔢 الإصدار: 1.0.0
echo 📅 تاريخ الإصدار: سبتمبر 2025
echo 💻 التقنية: C# + WPF + SQLite
echo 🎯 الهدف: إدارة البريد الوارد والصادر
echo.
echo 🔐 بيانات تسجيل الدخول الافتراضية:
echo ===================================
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo 🎯 الدور: مدير النظام
echo.
echo 📁 ملفات النظام:
echo ===============
if exist "DocFlowSystem\DocFlowSystem.csproj" (
    echo ✅ ملفات المشروع موجودة
) else (
    echo ❌ ملفات المشروع غير موجودة
)

if exist "DocFlowSystem\DocFlowDB.db" (
    echo ✅ قاعدة البيانات موجودة
    for %%A in ("DocFlowSystem\DocFlowDB.db") do echo 📏 حجم قاعدة البيانات: %%~zA بايت
) else (
    echo ❌ قاعدة البيانات غير موجودة
)

if exist "Backups" (
    dir "Backups\DocFlowDB_backup_*.db" /b 2>nul | find /c /v "" > temp_count.txt
    set /p backup_count=<temp_count.txt
    del temp_count.txt
    echo ✅ النسخ الاحتياطية: !backup_count! نسخة
) else (
    echo ❌ لا توجد نسخ احتياطية
)

echo.
echo 🖥️ معلومات النظام:
echo ==================
echo 💻 نظام التشغيل: %OS%
echo 👤 المستخدم: %USERNAME%
echo 📁 المجلد الحالي: %CD%

dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('dotnet --version') do echo ✅ .NET الإصدار: %%i
) else (
    echo ❌ .NET غير مثبت
)

echo.
pause
goto MAIN_MENU

:USER_GUIDE
cls
echo ╔════════════════════════════════════════╗
echo ║              دليل الاستخدام            ║
echo ╚════════════════════════════════════════╝
echo.
echo 📖 كيفية استخدام النظام:
echo ========================
echo.
echo 1️⃣ التشغيل الأول:
echo   - اختر "تشغيل النظام" من القائمة الرئيسية
echo   - انتظر حتى يتم تحضير النظام
echo   - سيفتح النظام في نافذة جديدة
echo.
echo 2️⃣ تسجيل الدخول:
echo   - اسم المستخدم: admin
echo   - كلمة المرور: admin123
echo   - اضغط "تسجيل الدخول"
echo.
echo 3️⃣ استخدام النظام:
echo   - استخدم القائمة الجانبية للتنقل
echo   - ابدأ بـ "قائمة البريد الوارد"
echo   - جرب إضافة بريد وارد جديد
echo   - استخدم البحث والتصفية
echo.
echo 4️⃣ النسخ الاحتياطي:
echo   - استخدم "النسخ الاحتياطي" بانتظام
echo   - احتفظ بالنسخ في مكان آمن
echo   - يمكن استعادة النسخ عند الحاجة
echo.
echo 5️⃣ الصيانة:
echo   - استخدم "أدوات الصيانة" لحل المشاكل
echo   - أعد بناء المشروع عند الحاجة
echo   - نظف ملفات البناء دورياً
echo.
echo 💡 نصائح مهمة:
echo ==============
echo - لا تغلق النظام أثناء حفظ البيانات
echo - اعمل نسخة احتياطية قبل التحديثات
echo - تأكد من صلاحيات الكتابة في المجلد
echo - أعد تشغيل الكمبيوتر عند مواجهة مشاكل
echo.
pause
goto MAIN_MENU

:EXIT
cls
echo.
echo ╔════════════════════════════════════════╗
echo ║              شكراً لك!                ║
echo ╚════════════════════════════════════════╝
echo.
echo 🎉 شكراً لاستخدام نظام إدارة البريد الحكومي
echo 📧 DocFlow Management System
echo.
echo 💡 لا تنس عمل نسخة احتياطية من بياناتك!
echo.
echo تم إنهاء البرنامج...
timeout /t 3 >nul
exit /b 0
