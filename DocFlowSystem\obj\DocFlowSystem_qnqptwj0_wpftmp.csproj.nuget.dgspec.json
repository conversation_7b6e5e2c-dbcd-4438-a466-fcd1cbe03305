{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\DocFlow_2025\\DocFlowSystem\\DocFlowSystem.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\DocFlow_2025\\DocFlowSystem\\DocFlowSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\DocFlow_2025\\DocFlowSystem\\DocFlowSystem.csproj", "projectName": "DocFlowSystem", "projectPath": "C:\\Users\\<USER>\\Desktop\\DocFlow_2025\\DocFlowSystem\\DocFlowSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\DocFlow_2025\\DocFlowSystem\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "EPPlus": {"target": "Package", "version": "[6.2.10, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Toolkit.Mvvm": {"target": "Package", "version": "[7.1.2, )"}, "iTextSharp": {"target": "Package", "version": "[********, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.305/PortableRuntimeIdentifierGraph.json"}}}}}