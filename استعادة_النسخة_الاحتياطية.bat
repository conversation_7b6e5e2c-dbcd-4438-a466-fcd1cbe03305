@echo off
chcp 65001 >nul
title استعادة النسخة الاحتياطية - نظام إدارة البريد

echo.
echo ========================================
echo       استعادة النسخة الاحتياطية
echo ========================================
echo.

:: Check if backup directory exists
if not exist "Backups" (
    echo ❌ مجلد النسخ الاحتياطية غير موجود
    echo تأكد من وجود مجلد Backups مع ملفات النسخ الاحتياطية
    pause
    exit /b 1
)

echo 📁 عرض النسخ الاحتياطية المتاحة:
echo ================================
echo.

:: List available backups
set backup_count=0
for %%f in ("Backups\DocFlowDB_backup_*.db") do (
    set /a backup_count+=1
    echo !backup_count!. %%~nxf
    echo    📅 تاريخ الإنشاء: %%~tf
    echo    📏 الحجم: %%~zf بايت
    echo.
)

if %backup_count% equ 0 (
    echo ❌ لا توجد نسخ احتياطية متاحة
    echo قم بإنشاء نسخة احتياطية أولاً باستخدام "نسخ_احتياطي.bat"
    pause
    exit /b 1
)

echo ⚠️ تحذير مهم:
echo =============
echo استعادة النسخة الاحتياطية ستؤدي إلى:
echo - حذف قاعدة البيانات الحالية
echo - فقدان جميع البيانات الحديثة
echo - استبدالها بالنسخة الاحتياطية المختارة
echo.

set /p confirm=هل تريد المتابعة؟ اكتب "نعم" للتأكيد: 
if /i not "%confirm%"=="نعم" (
    echo تم إلغاء العملية
    pause
    exit /b 0
)

echo.
echo أدخل اسم ملف النسخة الاحتياطية المراد استعادتها:
echo (مثال: DocFlowDB_backup_2025-09-13_14-30-25.db)
echo.
set /p backup_file=اسم الملف: 

:: Check if backup file exists
if not exist "Backups\%backup_file%" (
    echo ❌ الملف المحدد غير موجود
    echo تأكد من كتابة اسم الملف بشكل صحيح
    pause
    exit /b 1
)

echo.
echo 🔄 جاري استعادة النسخة الاحتياطية...

:: Stop any running instances (optional warning)
echo ⚠️ تأكد من إغلاق النظام قبل المتابعة
timeout /t 3 >nul

:: Backup current database (just in case)
if exist "DocFlowSystem\DocFlowDB.db" (
    echo 💾 إنشاء نسخة احتياطية من قاعدة البيانات الحالية...
    for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
    set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
    set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
    set "datestamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%"
    
    copy "DocFlowSystem\DocFlowDB.db" "Backups\DocFlowDB_before_restore_%datestamp%.db" >nul
    echo ✅ تم حفظ نسخة احتياطية من قاعدة البيانات الحالية
)

:: Remove current database files
echo 🗑️ حذف قاعدة البيانات الحالية...
if exist "DocFlowSystem\DocFlowDB.db" del "DocFlowSystem\DocFlowDB.db"
if exist "DocFlowSystem\DocFlowDB.db-shm" del "DocFlowSystem\DocFlowDB.db-shm"
if exist "DocFlowSystem\DocFlowDB.db-wal" del "DocFlowSystem\DocFlowDB.db-wal"

:: Restore backup
echo 📥 استعادة النسخة الاحتياطية...
copy "Backups\%backup_file%" "DocFlowSystem\DocFlowDB.db" >nul
if %errorlevel% equ 0 (
    echo ✅ تم استعادة النسخة الاحتياطية بنجاح
) else (
    echo ❌ فشل في استعادة النسخة الاحتياطية
    pause
    exit /b 1
)

:: Restore additional files if they exist
set "base_name=%backup_file:~0,-3%"
if exist "Backups\%base_name%.db-shm" (
    copy "Backups\%base_name%.db-shm" "DocFlowSystem\DocFlowDB.db-shm" >nul
    echo ✅ تم استعادة ملف .db-shm
)

if exist "Backups\%base_name%.db-wal" (
    copy "Backups\%base_name%.db-wal" "DocFlowSystem\DocFlowDB.db-wal" >nul
    echo ✅ تم استعادة ملف .db-wal
)

echo.
echo 🎉 تمت استعادة النسخة الاحتياطية بنجاح!
echo.
echo 📋 معلومات مهمة:
echo ================
echo - تم استعادة قاعدة البيانات من النسخة الاحتياطية
echo - يمكنك الآن تشغيل النظام بالبيانات المستعادة
echo - تم حفظ نسخة احتياطية من قاعدة البيانات السابقة
echo.
echo 🚀 لتشغيل النظام، استخدم "تشغيل_نظام_إدارة_البريد.bat"
echo.
pause
