using DocFlowSystem.Models;

namespace DocFlowSystem.Services.Interfaces
{
    public interface IReportService
    {
        Task<object> GetGeneralStatisticsAsync();
        Task<IEnumerable<IncomingMail>> GetIncomingMailReportAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<OutgoingMail>> GetOutgoingMailReportAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<object> GetUserActivityReportAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<byte[]> ExportToPdfAsync(object data, string reportType);
        Task<byte[]> ExportToExcelAsync(object data, string reportType);
    }
}
