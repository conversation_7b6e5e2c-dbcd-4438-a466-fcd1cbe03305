<UserControl x:Class="DocFlowSystem.Views.IncomingMail.AddIncomingMailView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             FlowDirection="RightToLeft"
             d:DesignHeight="700" d:DesignWidth="800">
    
    <UserControl.Resources>
        <Style x:Key="HeaderTextBlock" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>
        
        <Style x:Key="LabelTextBlock" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
            <Setter Property="VerticalAlignment" Value="Top"/>
        </Style>
        
        <Style x:Key="InputControl" TargetType="Control">
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Height" Value="35"/>
        </Style>
        
        <Style x:Key="SaveButton" TargetType="Button">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
        </Style>
        
        <Style x:Key="CancelButton" TargetType="Button">
            <Setter Property="Background" Value="#F44336"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <TextBlock Grid.Row="0" Text="➕ إضافة بريد وارد جديد" Style="{StaticResource HeaderTextBlock}"/>

            <!-- Form -->
            <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="30" Margin="0,0,0,20">
                <Border.Effect>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
                </Border.Effect>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Left Column -->
                    <StackPanel Grid.Column="0">
                        <!-- Incoming Number -->
                        <TextBlock Text="الرقم الوارد:" Style="{StaticResource LabelTextBlock}"/>
                        <TextBox x:Name="IncomingNumberTextBox" 
                                Style="{StaticResource InputControl}"
                                IsReadOnly="True" 
                                Background="#F5F5F5"
                                Text="سيتم إنشاؤه تلقائياً"/>

                        <!-- Sender Entity -->
                        <TextBlock Text="الجهة المرسلة: *" Style="{StaticResource LabelTextBlock}"/>
                        <TextBox x:Name="SenderEntityTextBox" Style="{StaticResource InputControl}"/>

                        <!-- Subject -->
                        <TextBlock Text="الموضوع: *" Style="{StaticResource LabelTextBlock}"/>
                        <TextBox x:Name="SubjectTextBox" Style="{StaticResource InputControl}"/>

                        <!-- Received Date -->
                        <TextBlock Text="تاريخ الاستلام: *" Style="{StaticResource LabelTextBlock}"/>
                        <DatePicker x:Name="ReceivedDatePicker" 
                                   Style="{StaticResource InputControl}"
                                   SelectedDate="{x:Static sys:DateTime.Now}"
                                   xmlns:sys="clr-namespace:System;assembly=mscorlib"/>

                        <!-- Priority -->
                        <TextBlock Text="الأولوية:" Style="{StaticResource LabelTextBlock}"/>
                        <ComboBox x:Name="PriorityComboBox" Style="{StaticResource InputControl}">
                            <ComboBoxItem Content="منخفض"/>
                            <ComboBoxItem Content="متوسط" IsSelected="True"/>
                            <ComboBoxItem Content="عالي"/>
                        </ComboBox>
                    </StackPanel>

                    <!-- Right Column -->
                    <StackPanel Grid.Column="2">
                        <!-- Status -->
                        <TextBlock Text="الحالة:" Style="{StaticResource LabelTextBlock}"/>
                        <ComboBox x:Name="StatusComboBox" Style="{StaticResource InputControl}">
                            <ComboBoxItem Content="جديد" IsSelected="True"/>
                            <ComboBoxItem Content="قيد الإجراء"/>
                            <ComboBoxItem Content="منجز"/>
                        </ComboBox>

                        <!-- Assigned To User -->
                        <TextBlock Text="المسؤول:" Style="{StaticResource LabelTextBlock}"/>
                        <ComboBox x:Name="AssignedToUserComboBox" 
                                 Style="{StaticResource InputControl}"
                                 DisplayMemberPath="FullName"
                                 SelectedValuePath="UserId"/>

                        <!-- Content -->
                        <TextBlock Text="المحتوى:" Style="{StaticResource LabelTextBlock}"/>
                        <TextBox x:Name="ContentTextBox" 
                                TextWrapping="Wrap" 
                                AcceptsReturn="True"
                                VerticalScrollBarVisibility="Auto"
                                Height="120" 
                                Padding="8" 
                                Margin="0,0,0,15"/>

                        <!-- Notes -->
                        <TextBlock Text="ملاحظات:" Style="{StaticResource LabelTextBlock}"/>
                        <TextBox x:Name="NotesTextBox" 
                                TextWrapping="Wrap" 
                                AcceptsReturn="True"
                                VerticalScrollBarVisibility="Auto"
                                Height="80" 
                                Padding="8" 
                                Margin="0,0,0,15"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Action Buttons -->
            <Border Grid.Row="2" Background="White" CornerRadius="8" Padding="20">
                <Border.Effect>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
                </Border.Effect>
                
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="SaveButton" Content="💾 حفظ" 
                           Style="{StaticResource SaveButton}"
                           Click="SaveButton_Click"/>
                    
                    <Button x:Name="SaveAndNewButton" Content="💾 حفظ وإضافة جديد" 
                           Style="{StaticResource SaveButton}"
                           Background="#2196F3"
                           Click="SaveAndNewButton_Click"/>
                    
                    <Button x:Name="CancelButton" Content="❌ إلغاء" 
                           Style="{StaticResource CancelButton}"
                           Click="CancelButton_Click"/>
                </StackPanel>
            </Border>

            <!-- Loading Indicator -->
            <Border x:Name="LoadingBorder" 
                   Grid.RowSpan="3"
                   Background="#80000000" 
                   Visibility="Collapsed">
                <Border HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Background="White"
                       Padding="30"
                       CornerRadius="10">
                    <StackPanel>
                    <ProgressBar Width="200" Height="20" IsIndeterminate="True" Margin="0,0,0,15"/>
                        <TextBlock x:Name="LoadingTextBlock"
                                  Text="جاري الحفظ..."
                                  HorizontalAlignment="Center"
                                  FontWeight="Bold"/>
                    </StackPanel>
                </Border>
            </Border>
        </Grid>
    </ScrollViewer>
</UserControl>
