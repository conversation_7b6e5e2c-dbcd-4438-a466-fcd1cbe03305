using System.IO;
using System.Windows;
using System.Windows.Controls;
using DocFlowSystem.Services.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace DocFlowSystem.Views.Settings
{
    public partial class SettingsView : UserControl
    {
        private readonly IServiceProvider _serviceProvider;

        public SettingsView(IServiceProvider serviceProvider)
        {
            InitializeComponent();
            _serviceProvider = serviceProvider;

            Loaded += SettingsView_Loaded;
        }

        private async void SettingsView_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadSettingsAsync();
            await CalculateStorageUsageAsync();
        }

        private async Task LoadSettingsAsync()
        {
            try
            {
                // Simulate async loading with a small delay
                await Task.Delay(10);

                // Load current settings - for now, use default values
                // In a real app, these would be loaded from a configuration file or database
                AutoBackupCheckBox.IsChecked = true;
                BackupIntervalTextBox.Text = "7";
                ShowNotificationsCheckBox.IsChecked = true;
                AutoSaveCheckBox.IsChecked = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CalculateStorageUsageAsync()
        {
            try
            {
                // Run file operations on background thread
                await Task.Run(() =>
                {
                    var databasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "DocFlowDB.db");

                    if (File.Exists(databasePath))
                    {
                        var fileInfo = new FileInfo(databasePath);
                        var sizeInMB = fileInfo.Length / (1024.0 * 1024.0);

                        // Update UI on main thread
                        Dispatcher.Invoke(() =>
                        {
                            StorageUsageTextBlock.Text = $"{sizeInMB:F2} ميجابايت";
                        });
                    }
                    else
                    {
                        Dispatcher.Invoke(() =>
                        {
                            StorageUsageTextBlock.Text = "قاعدة البيانات غير موجودة";
                        });
                    }
                });
            }
            catch (Exception ex)
            {
                StorageUsageTextBlock.Text = $"خطأ في الحساب: {ex.Message}";
            }
        }

        private void BackupButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var databasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "DocFlowDB.db");

                if (!File.Exists(databasePath))
                {
                    MessageBox.Show("قاعدة البيانات غير موجودة", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // Create backup directory if it doesn't exist
                var backupDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups");
                Directory.CreateDirectory(backupDir);

                // Generate backup filename with timestamp
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupPath = Path.Combine(backupDir, $"DocFlowDB_Backup_{timestamp}.db");

                // Copy the database file
                File.Copy(databasePath, backupPath, true);

                MessageBox.Show($"تم إنشاء النسخة الاحتياطية بنجاح:\n{backupPath}",
                    "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {ex.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RestoreButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "اختر ملف النسخة الاحتياطية",
                    Filter = "Database files (*.db)|*.db|All files (*.*)|*.*",
                    InitialDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups")
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var result = MessageBox.Show(
                        "هل تريد بالتأكيد استعادة قاعدة البيانات؟\n\nسيتم استبدال البيانات الحالية بالنسخة الاحتياطية.",
                        "تأكيد الاستعادة",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        var databasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "DocFlowDB.db");

                        // Create backup of current database before restore
                        if (File.Exists(databasePath))
                        {
                            var currentBackup = Path.Combine(
                                AppDomain.CurrentDomain.BaseDirectory,
                                "Backups",
                                $"DocFlowDB_BeforeRestore_{DateTime.Now.ToString("yyyyMMdd_HHmmss")}.db");
                            File.Copy(databasePath, currentBackup, true);
                        }

                        // Restore from backup
                        File.Copy(openFileDialog.FileName, databasePath, true);

                        MessageBox.Show("تم استعادة قاعدة البيانات بنجاح\n\nيُنصح بإعادة تشغيل التطبيق",
                            "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء استعادة قاعدة البيانات: {ex.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate backup interval
                if (!int.TryParse(BackupIntervalTextBox.Text, out int interval) || interval < 1)
                {
                    MessageBox.Show("يرجى إدخال فترة نسخ احتياطي صحيحة (أكبر من 0)",
                        "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    BackupIntervalTextBox.Focus();
                    return;
                }

                // Save settings - for now, just show success message
                // In a real app, these would be saved to a configuration file or database
                MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ الإعدادات: {ex.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ResetSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
                "تأكيد إعادة التعيين", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // Reset to default values
                AutoBackupCheckBox.IsChecked = true;
                BackupIntervalTextBox.Text = "7";
                ShowNotificationsCheckBox.IsChecked = true;
                AutoSaveCheckBox.IsChecked = true;

                MessageBox.Show("تم إعادة تعيين الإعدادات إلى القيم الافتراضية",
                    "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }
}
