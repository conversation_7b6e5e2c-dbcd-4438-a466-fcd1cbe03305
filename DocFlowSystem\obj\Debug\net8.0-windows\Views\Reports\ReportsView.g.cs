﻿#pragma checksum "..\..\..\..\..\Views\Reports\ReportsView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "EEF1CA7B56A3476509EFA7104E480C606B27D029"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DocFlowSystem.Views.Reports {
    
    
    /// <summary>
    /// ReportsView
    /// </summary>
    public partial class ReportsView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 64 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button IncomingMailReportButton;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OutgoingMailReportButton;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StatisticsReportButton;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UserActivityReportButton;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReportTitleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ReportContentPanel;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportToPdfButton;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportToExcelButton;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintReportButton;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border LoadingBorder;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.9.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DocFlowSystem;V1.0.0.0;component/views/reports/reportsview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.9.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.IncomingMailReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 66 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
            this.IncomingMailReportButton.Click += new System.Windows.RoutedEventHandler(this.IncomingMailReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.OutgoingMailReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
            this.OutgoingMailReportButton.Click += new System.Windows.RoutedEventHandler(this.OutgoingMailReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.StatisticsReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 75 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
            this.StatisticsReportButton.Click += new System.Windows.RoutedEventHandler(this.StatisticsReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.UserActivityReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 80 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
            this.UserActivityReportButton.Click += new System.Windows.RoutedEventHandler(this.UserActivityReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ReportTitleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.ReportContentPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 7:
            this.ExportToPdfButton = ((System.Windows.Controls.Button)(target));
            
            #line 137 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
            this.ExportToPdfButton.Click += new System.Windows.RoutedEventHandler(this.ExportToPdfButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ExportToExcelButton = ((System.Windows.Controls.Button)(target));
            
            #line 143 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
            this.ExportToExcelButton.Click += new System.Windows.RoutedEventHandler(this.ExportToExcelButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.PrintReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 149 "..\..\..\..\..\Views\Reports\ReportsView.xaml"
            this.PrintReportButton.Click += new System.Windows.RoutedEventHandler(this.PrintReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.LoadingBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 12:
            this.LoadingTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

