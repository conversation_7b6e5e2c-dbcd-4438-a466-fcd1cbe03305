using System.Windows;
using System.Windows.Controls;
using DocFlowSystem.Services.Interfaces;
using DocFlowSystem.Models;
using Microsoft.Win32;
using System.IO;

namespace DocFlowSystem.Views.Reports
{
    public partial class ReportsView : UserControl
    {
        private readonly IIncomingMailService _incomingMailService;
        private readonly IOutgoingMailService _outgoingMailService;
        private readonly IReportService _reportService;
        private object? _currentReportData;
        private string _currentReportType = "";

        public ReportsView(IIncomingMailService incomingMailService, 
                          IOutgoingMailService outgoingMailService,
                          IReportService reportService)
        {
            InitializeComponent();
            _incomingMailService = incomingMailService;
            _outgoingMailService = outgoingMailService;
            _reportService = reportService;
        }

        private async void IncomingMailReportButton_Click(object sender, RoutedEventArgs e)
        {
            await GenerateIncomingMailReportAsync();
        }

        private async void OutgoingMailReportButton_Click(object sender, RoutedEventArgs e)
        {
            await GenerateOutgoingMailReportAsync();
        }

        private async void StatisticsReportButton_Click(object sender, RoutedEventArgs e)
        {
            await GenerateStatisticsReportAsync();
        }

        private async void UserActivityReportButton_Click(object sender, RoutedEventArgs e)
        {
            await GenerateUserActivityReportAsync();
        }

        private async Task GenerateIncomingMailReportAsync()
        {
            try
            {
                ShowLoading(true, "جاري إنشاء تقرير البريد الوارد...");
                
                var incomingMails = await _incomingMailService.GetAllAsync();
                _currentReportData = incomingMails.ToList();
                _currentReportType = "IncomingMail";
                
                ReportTitleTextBlock.Text = "📥 تقرير البريد الوارد";
                
                // Create report content
                var reportContent = CreateIncomingMailReportContent(incomingMails);
                ReportContentPanel.Children.Clear();
                ReportContentPanel.Children.Add(reportContent);
                
                EnableExportButtons(true);
                StatusTextBlock.Text = $"تم إنشاء التقرير - {incomingMails.Count()} عنصر";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "فشل في إنشاء التقرير";
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private async Task GenerateOutgoingMailReportAsync()
        {
            try
            {
                ShowLoading(true, "جاري إنشاء تقرير البريد الصادر...");
                
                var outgoingMails = await _outgoingMailService.GetAllAsync();
                _currentReportData = outgoingMails.ToList();
                _currentReportType = "OutgoingMail";
                
                ReportTitleTextBlock.Text = "📤 تقرير البريد الصادر";
                
                // Create report content
                var reportContent = CreateOutgoingMailReportContent(outgoingMails);
                ReportContentPanel.Children.Clear();
                ReportContentPanel.Children.Add(reportContent);
                
                EnableExportButtons(true);
                StatusTextBlock.Text = $"تم إنشاء التقرير - {outgoingMails.Count()} عنصر";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "فشل في إنشاء التقرير";
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private async Task GenerateStatisticsReportAsync()
        {
            try
            {
                ShowLoading(true, "جاري إنشاء الإحصائيات العامة...");
                
                var statistics = await _reportService.GetGeneralStatisticsAsync();
                _currentReportData = statistics;
                _currentReportType = "Statistics";
                
                ReportTitleTextBlock.Text = "📈 الإحصائيات العامة";
                
                // Create statistics content
                var reportContent = CreateStatisticsReportContent(statistics);
                ReportContentPanel.Children.Clear();
                ReportContentPanel.Children.Add(reportContent);
                
                EnableExportButtons(true);
                StatusTextBlock.Text = "تم إنشاء الإحصائيات";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء الإحصائيات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusTextBlock.Text = "فشل في إنشاء الإحصائيات";
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private async Task GenerateUserActivityReportAsync()
        {
            try
            {
                ShowLoading(true, "جاري إنشاء تقرير نشاط المستخدمين...");

                // Simulate async operation
                await Task.Delay(500);

                // For now, show a coming soon message
                ReportTitleTextBlock.Text = "👥 تقرير نشاط المستخدمين";
                
                var comingSoonContent = new Border
                {
                    Background = System.Windows.Media.Brushes.LightYellow,
                    CornerRadius = new CornerRadius(5),
                    Padding = new Thickness(20),
                    HorizontalAlignment = HorizontalAlignment.Center
                };
                
                var stackPanel = new StackPanel { HorizontalAlignment = HorizontalAlignment.Center };
                stackPanel.Children.Add(new TextBlock 
                { 
                    Text = "🚧", 
                    FontSize = 48, 
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 10)
                });
                stackPanel.Children.Add(new TextBlock 
                { 
                    Text = "تقرير نشاط المستخدمين قيد التطوير", 
                    FontSize = 16, 
                    HorizontalAlignment = HorizontalAlignment.Center,
                    FontWeight = FontWeights.Bold
                });
                
                comingSoonContent.Child = stackPanel;
                
                ReportContentPanel.Children.Clear();
                ReportContentPanel.Children.Add(comingSoonContent);
                
                EnableExportButtons(false);
                StatusTextBlock.Text = "قيد التطوير";
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private UIElement CreateIncomingMailReportContent(IEnumerable<Models.IncomingMail> incomingMails)
        {
            var dataGrid = new DataGrid
            {
                AutoGenerateColumns = false,
                CanUserAddRows = false,
                CanUserDeleteRows = false,
                IsReadOnly = true,
                GridLinesVisibility = DataGridGridLinesVisibility.Horizontal,
                HeadersVisibility = DataGridHeadersVisibility.Column,
                AlternatingRowBackground = System.Windows.Media.Brushes.LightGray
            };

            // Add columns
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الرقم الوارد", Binding = new System.Windows.Data.Binding("IncomingNumber") });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الجهة المرسلة", Binding = new System.Windows.Data.Binding("SenderEntity") });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الموضوع", Binding = new System.Windows.Data.Binding("Subject") });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "تاريخ الاستلام", Binding = new System.Windows.Data.Binding("ReceivedDate") { StringFormat = "yyyy/MM/dd" } });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الحالة", Binding = new System.Windows.Data.Binding("Status") });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الأولوية", Binding = new System.Windows.Data.Binding("Priority") });

            dataGrid.ItemsSource = incomingMails;
            return dataGrid;
        }

        private UIElement CreateOutgoingMailReportContent(IEnumerable<Models.OutgoingMail> outgoingMails)
        {
            var dataGrid = new DataGrid
            {
                AutoGenerateColumns = false,
                CanUserAddRows = false,
                CanUserDeleteRows = false,
                IsReadOnly = true,
                GridLinesVisibility = DataGridGridLinesVisibility.Horizontal,
                HeadersVisibility = DataGridHeadersVisibility.Column,
                AlternatingRowBackground = System.Windows.Media.Brushes.LightGray
            };

            // Add columns
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الرقم الصادر", Binding = new System.Windows.Data.Binding("OutgoingNumber") });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الجهة المستقبلة", Binding = new System.Windows.Data.Binding("RecipientEntity") });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الموضوع", Binding = new System.Windows.Data.Binding("Subject") });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "تاريخ الإرسال", Binding = new System.Windows.Data.Binding("SentDate") { StringFormat = "yyyy/MM/dd" } });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الحالة", Binding = new System.Windows.Data.Binding("Status") });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "الأولوية", Binding = new System.Windows.Data.Binding("Priority") });

            dataGrid.ItemsSource = outgoingMails;
            return dataGrid;
        }

        private UIElement CreateStatisticsReportContent(object statistics)
        {
            // Create a simple statistics display
            var stackPanel = new StackPanel();
            
            // Add some sample statistics
            stackPanel.Children.Add(CreateStatisticItem("📥 إجمالي البريد الوارد", "150"));
            stackPanel.Children.Add(CreateStatisticItem("📤 إجمالي البريد الصادر", "89"));
            stackPanel.Children.Add(CreateStatisticItem("⏳ البريد المعلق", "12"));
            stackPanel.Children.Add(CreateStatisticItem("✅ البريد المكتمل", "227"));
            stackPanel.Children.Add(CreateStatisticItem("👥 المستخدمين النشطين", "5"));
            
            return stackPanel;
        }

        private Border CreateStatisticItem(string label, string value)
        {
            var border = new Border
            {
                Background = System.Windows.Media.Brushes.LightBlue,
                CornerRadius = new CornerRadius(5),
                Padding = new Thickness(15),
                Margin = new Thickness(0, 5, 0, 0)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var labelTextBlock = new TextBlock
            {
                Text = label,
                FontWeight = FontWeights.Bold,
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(labelTextBlock, 0);

            var valueTextBlock = new TextBlock
            {
                Text = value,
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = System.Windows.Media.Brushes.DarkBlue,
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(valueTextBlock, 1);

            grid.Children.Add(labelTextBlock);
            grid.Children.Add(valueTextBlock);
            border.Child = grid;

            return border;
        }

        private async void ExportToPdfButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentReportData == null) return;

            var saveFileDialog = new SaveFileDialog
            {
                Filter = "PDF files (*.pdf)|*.pdf",
                DefaultExt = "pdf",
                FileName = $"تقرير_{_currentReportType}_{DateTime.Now:yyyy-MM-dd}.pdf"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    ShowLoading(true, "جاري تصدير PDF...");
                    
                    // TODO: Implement PDF export
                    MessageBox.Show("تم تصدير التقرير إلى PDF بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    StatusTextBlock.Text = "تم تصدير PDF";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء تصدير PDF: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    ShowLoading(false);
                }
            }
        }

        private async void ExportToExcelButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentReportData == null) return;

            var saveFileDialog = new SaveFileDialog
            {
                Filter = "Excel files (*.xlsx)|*.xlsx",
                DefaultExt = "xlsx",
                FileName = $"تقرير_{_currentReportType}_{DateTime.Now:yyyy-MM-dd}.xlsx"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    ShowLoading(true, "جاري تصدير Excel...");
                    
                    // TODO: Implement Excel export
                    MessageBox.Show("تم تصدير التقرير إلى Excel بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    StatusTextBlock.Text = "تم تصدير Excel";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء تصدير Excel: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    ShowLoading(false);
                }
            }
        }

        private void PrintReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // TODO: Implement print functionality
                MessageBox.Show("وظيفة الطباعة قيد التطوير", "معلومات", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EnableExportButtons(bool enabled)
        {
            ExportToPdfButton.IsEnabled = enabled;
            ExportToExcelButton.IsEnabled = enabled;
            PrintReportButton.IsEnabled = enabled;
        }

        private void ShowLoading(bool show, string message = "جاري المعالجة...")
        {
            LoadingBorder.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            LoadingTextBlock.Text = message;
        }
    }
}
