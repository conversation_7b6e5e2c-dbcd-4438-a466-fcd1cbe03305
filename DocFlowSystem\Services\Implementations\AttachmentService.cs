using DocFlowSystem.Data.Repositories;
using DocFlowSystem.Models;
using DocFlowSystem.Services.Interfaces;

namespace DocFlowSystem.Services.Implementations
{
    public class AttachmentService : IAttachmentService
    {
        private readonly IUnitOfWork _unitOfWork;

        public AttachmentService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<IEnumerable<Attachment>> GetAllAsync()
        {
            return await _unitOfWork.Attachments.GetAllAsync();
        }

        public async Task<Attachment?> GetByIdAsync(int id)
        {
            return await _unitOfWork.Attachments.GetByIdAsync(id);
        }

        public async Task<Attachment> CreateAsync(Attachment attachment)
        {
            attachment.UploadedDate = DateTime.Now;
            
            await _unitOfWork.Attachments.AddAsync(attachment);
            await _unitOfWork.SaveChangesAsync();

            await LogActivityAsync(attachment.UploadedByUserId, "إضافة", "Attachments", attachment.AttachmentId, 
                $"رفع مرفق: {attachment.FileName}");

            return attachment;
        }

        public async Task<Attachment> UpdateAsync(Attachment attachment)
        {
            _unitOfWork.Attachments.Update(attachment);
            await _unitOfWork.SaveChangesAsync();

            await LogActivityAsync(attachment.UploadedByUserId, "تعديل", "Attachments", attachment.AttachmentId);

            return attachment;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var attachment = await _unitOfWork.Attachments.GetByIdAsync(id);
                if (attachment == null) return false;

                _unitOfWork.Attachments.Remove(attachment);
                await _unitOfWork.SaveChangesAsync();

                await LogActivityAsync(attachment.UploadedByUserId, "حذف", "Attachments", id, 
                    $"حذف مرفق: {attachment.FileName}");

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<IEnumerable<Attachment>> GetByEntityAsync(int entityId, string entityType)
        {
            return await _unitOfWork.Attachments.FindAsync(a => a.EntityId == entityId && a.EntityType == entityType);
        }

        public async Task<bool> ExistsAsync(int id)
        {
            var attachment = await _unitOfWork.Attachments.GetByIdAsync(id);
            return attachment != null;
        }

        public async Task<long> GetTotalSizeByEntityAsync(int entityId, string entityType)
        {
            var attachments = await GetByEntityAsync(entityId, entityType);
            return attachments.Sum(a => a.FileSize);
        }

        public async Task<int> GetCountByEntityAsync(int entityId, string entityType)
        {
            var attachments = await GetByEntityAsync(entityId, entityType);
            return attachments.Count();
        }

        private async Task LogActivityAsync(int userId, string action, string tableName, int recordId, string? description = null)
        {
            try
            {
                var log = new Log
                {
                    UserId = userId,
                    Action = action,
                    TableName = tableName,
                    RecordId = recordId,
                    NewValues = description,
                    IPAddress = "127.0.0.1",
                    CreatedDate = DateTime.Now
                };

                await _unitOfWork.Logs.AddAsync(log);
                await _unitOfWork.SaveChangesAsync();
            }
            catch (Exception)
            {
                // Log errors should not break the main functionality
            }
        }
    }
}
