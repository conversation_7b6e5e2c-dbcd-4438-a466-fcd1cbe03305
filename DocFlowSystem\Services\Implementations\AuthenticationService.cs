using BCrypt.Net;
using DocFlowSystem.Data.Repositories;
using DocFlowSystem.Models;
using DocFlowSystem.Services.Interfaces;

namespace DocFlowSystem.Services.Implementations
{
    public class AuthenticationService : IAuthenticationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private User? _currentUser;

        public AuthenticationService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<User?> LoginAsync(string username, string password)
        {
            try
            {
                var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Username == username && u.IsActive);
                
                if (user == null || !VerifyPassword(password, user.PasswordHash))
                {
                    return null;
                }

                // Update last login date
                user.LastLoginDate = DateTime.Now;
                _unitOfWork.Users.Update(user);
                await _unitOfWork.SaveChangesAsync();

                // Log login activity
                await LogActivityAsync(user.UserId, "تسجيل دخول", null, null, null);

                _currentUser = user;
                return user;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task<bool> LogoutAsync(int userId)
        {
            try
            {
                await LogActivityAsync(userId, "تسجيل خروج", null, null, null);
                _currentUser = null;
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByIdAsync(userId);
                if (user == null || !VerifyPassword(currentPassword, user.PasswordHash))
                {
                    return false;
                }

                user.PasswordHash = HashPassword(newPassword);
                _unitOfWork.Users.Update(user);
                await _unitOfWork.SaveChangesAsync();

                await LogActivityAsync(userId, "تعديل", "Users", userId, "تغيير كلمة المرور");

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public Task<User?> GetCurrentUserAsync()
        {
            return Task.FromResult(_currentUser);
        }

        public async Task<bool> HasPermissionAsync(int userId, string permission)
        {
            try
            {
                var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.UserId == userId);
                if (user == null) return false;

                var role = await _unitOfWork.UserRoles.GetByIdAsync(user.UserRoleId);
                if (role == null) return false;

                // Simple role-based permissions
                return role.RoleName switch
                {
                    "مدير" => true, // Admin has all permissions
                    "مستخدم" => permission != "إدارة_المستخدمين" && permission != "النسخ_الاحتياطي",
                    "أرشيف" => permission == "البحث" || permission == "عرض_التقارير" || permission == "الأرشفة",
                    _ => false
                };
            }
            catch (Exception)
            {
                return false;
            }
        }

        public string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt(12));
        }

        public bool VerifyPassword(string password, string hashedPassword)
        {
            try
            {
                return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
            }
            catch (Exception)
            {
                return false;
            }
        }

        private async Task LogActivityAsync(int userId, string action, string? tableName, int? recordId, string? description)
        {
            try
            {
                var log = new Log
                {
                    UserId = userId,
                    Action = action,
                    TableName = tableName,
                    RecordId = recordId,
                    NewValues = description,
                    IPAddress = "127.0.0.1", // In a real app, get actual IP
                    CreatedDate = DateTime.Now
                };

                await _unitOfWork.Logs.AddAsync(log);
                await _unitOfWork.SaveChangesAsync();
            }
            catch (Exception)
            {
                // Log errors should not break the main functionality
            }
        }
    }
}
