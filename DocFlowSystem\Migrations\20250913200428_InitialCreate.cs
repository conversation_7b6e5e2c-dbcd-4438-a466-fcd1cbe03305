﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace DocFlowSystem.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "UserRoles",
                columns: table => new
                {
                    RoleId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    RoleName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')"),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserRoles", x => x.RoleId);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    UserId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Username = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    PasswordHash = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    FullName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Email = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Department = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    RoleId = table.Column<int>(type: "INTEGER", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')"),
                    LastLoginDate = table.Column<DateTime>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.UserId);
                    table.ForeignKey(
                        name: "FK_Users_UserRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "UserRoles",
                        principalColumn: "RoleId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "IncomingMails",
                columns: table => new
                {
                    IncomingMailId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    IncomingNumber = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    SenderEntity = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Subject = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    Content = table.Column<string>(type: "TEXT", nullable: true),
                    ReceivedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Priority = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false, defaultValue: "متوسط"),
                    Status = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false, defaultValue: "جديد"),
                    AssignedToUserId = table.Column<int>(type: "INTEGER", nullable: true),
                    CreatedByUserId = table.Column<int>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')"),
                    UpdatedDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Notes = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_IncomingMails", x => x.IncomingMailId);
                    table.ForeignKey(
                        name: "FK_IncomingMails_Users_AssignedToUserId",
                        column: x => x.AssignedToUserId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_IncomingMails_Users_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Logs",
                columns: table => new
                {
                    LogId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    UserId = table.Column<int>(type: "INTEGER", nullable: false),
                    Action = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    TableName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    RecordId = table.Column<int>(type: "INTEGER", nullable: true),
                    OldValues = table.Column<string>(type: "TEXT", nullable: true),
                    NewValues = table.Column<string>(type: "TEXT", nullable: true),
                    IPAddress = table.Column<string>(type: "TEXT", maxLength: 45, nullable: true),
                    UserAgent = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Logs", x => x.LogId);
                    table.ForeignKey(
                        name: "FK_Logs_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "OutgoingMails",
                columns: table => new
                {
                    OutgoingMailId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    OutgoingNumber = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    RecipientEntity = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Subject = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    Content = table.Column<string>(type: "TEXT", nullable: true),
                    SentDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Priority = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false, defaultValue: "متوسط"),
                    Status = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false, defaultValue: "مسودة"),
                    RelatedIncomingMailId = table.Column<int>(type: "INTEGER", nullable: true),
                    CreatedByUserId = table.Column<int>(type: "INTEGER", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')"),
                    UpdatedDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Notes = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OutgoingMails", x => x.OutgoingMailId);
                    table.ForeignKey(
                        name: "FK_OutgoingMails_IncomingMails_RelatedIncomingMailId",
                        column: x => x.RelatedIncomingMailId,
                        principalTable: "IncomingMails",
                        principalColumn: "IncomingMailId",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_OutgoingMails_Users_CreatedByUserId",
                        column: x => x.CreatedByUserId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Attachments",
                columns: table => new
                {
                    AttachmentId = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    FileName = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    OriginalFileName = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    FilePath = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    FileSize = table.Column<long>(type: "INTEGER", nullable: false),
                    FileType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    IncomingMailId = table.Column<int>(type: "INTEGER", nullable: true),
                    OutgoingMailId = table.Column<int>(type: "INTEGER", nullable: true),
                    UploadedByUserId = table.Column<int>(type: "INTEGER", nullable: false),
                    UploadedDate = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Attachments", x => x.AttachmentId);
                    table.CheckConstraint("CHK_Attachment_Mail", "(IncomingMailId IS NOT NULL AND OutgoingMailId IS NULL) OR (IncomingMailId IS NULL AND OutgoingMailId IS NOT NULL)");
                    table.ForeignKey(
                        name: "FK_Attachments_IncomingMails_IncomingMailId",
                        column: x => x.IncomingMailId,
                        principalTable: "IncomingMails",
                        principalColumn: "IncomingMailId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Attachments_OutgoingMails_OutgoingMailId",
                        column: x => x.OutgoingMailId,
                        principalTable: "OutgoingMails",
                        principalColumn: "OutgoingMailId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Attachments_Users_UploadedByUserId",
                        column: x => x.UploadedByUserId,
                        principalTable: "Users",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "UserRoles",
                columns: new[] { "RoleId", "CreatedDate", "Description", "IsActive", "RoleName" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 9, 13, 22, 4, 27, 554, DateTimeKind.Local).AddTicks(2828), "مدير النظام - صلاحيات كاملة", true, "مدير" },
                    { 2, new DateTime(2025, 9, 13, 22, 4, 27, 554, DateTimeKind.Local).AddTicks(2830), "مستخدم عادي - صلاحيات محدودة", true, "مستخدم" },
                    { 3, new DateTime(2025, 9, 13, 22, 4, 27, 554, DateTimeKind.Local).AddTicks(2832), "مسؤول الأرشيف - صلاحيات الأرشفة والبحث", true, "أرشيف" }
                });

            migrationBuilder.InsertData(
                table: "Users",
                columns: new[] { "UserId", "CreatedDate", "Department", "Email", "FullName", "IsActive", "LastLoginDate", "PasswordHash", "RoleId", "Username" },
                values: new object[] { 1, new DateTime(2025, 9, 13, 22, 4, 27, 554, DateTimeKind.Local).AddTicks(2974), "إدارة تقنية المعلومات", "<EMAIL>", "مدير النظام", true, null, "$2a$11$rQZr5EZ5Z5Z5Z5Z5Z5Z5ZOK5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5", 1, "admin" });

            migrationBuilder.CreateIndex(
                name: "IX_Attachments_IncomingMailId",
                table: "Attachments",
                column: "IncomingMailId");

            migrationBuilder.CreateIndex(
                name: "IX_Attachments_OutgoingMailId",
                table: "Attachments",
                column: "OutgoingMailId");

            migrationBuilder.CreateIndex(
                name: "IX_Attachments_UploadedByUserId",
                table: "Attachments",
                column: "UploadedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_IncomingMails_AssignedToUserId",
                table: "IncomingMails",
                column: "AssignedToUserId");

            migrationBuilder.CreateIndex(
                name: "IX_IncomingMails_CreatedByUserId",
                table: "IncomingMails",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_IncomingMails_IncomingNumber",
                table: "IncomingMails",
                column: "IncomingNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_IncomingMails_ReceivedDate",
                table: "IncomingMails",
                column: "ReceivedDate");

            migrationBuilder.CreateIndex(
                name: "IX_IncomingMails_SenderEntity",
                table: "IncomingMails",
                column: "SenderEntity");

            migrationBuilder.CreateIndex(
                name: "IX_IncomingMails_Status",
                table: "IncomingMails",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Logs_CreatedDate",
                table: "Logs",
                column: "CreatedDate");

            migrationBuilder.CreateIndex(
                name: "IX_Logs_TableName_RecordId",
                table: "Logs",
                columns: new[] { "TableName", "RecordId" });

            migrationBuilder.CreateIndex(
                name: "IX_Logs_UserId",
                table: "Logs",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_OutgoingMails_CreatedByUserId",
                table: "OutgoingMails",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_OutgoingMails_OutgoingNumber",
                table: "OutgoingMails",
                column: "OutgoingNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_OutgoingMails_RecipientEntity",
                table: "OutgoingMails",
                column: "RecipientEntity");

            migrationBuilder.CreateIndex(
                name: "IX_OutgoingMails_RelatedIncomingMailId",
                table: "OutgoingMails",
                column: "RelatedIncomingMailId");

            migrationBuilder.CreateIndex(
                name: "IX_OutgoingMails_SentDate",
                table: "OutgoingMails",
                column: "SentDate");

            migrationBuilder.CreateIndex(
                name: "IX_OutgoingMails_Status",
                table: "OutgoingMails",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_RoleName",
                table: "UserRoles",
                column: "RoleName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Users_RoleId",
                table: "Users",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_Username",
                table: "Users",
                column: "Username",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Attachments");

            migrationBuilder.DropTable(
                name: "Logs");

            migrationBuilder.DropTable(
                name: "OutgoingMails");

            migrationBuilder.DropTable(
                name: "IncomingMails");

            migrationBuilder.DropTable(
                name: "Users");

            migrationBuilder.DropTable(
                name: "UserRoles");
        }
    }
}
