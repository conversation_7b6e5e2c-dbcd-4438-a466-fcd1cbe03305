@echo off
chcp 65001 >nul
title نسخ احتياطي - نظام إدارة البريد

echo.
echo ========================================
echo        نسخ احتياطي لقاعدة البيانات
echo ========================================
echo.

:: Create backup directory if it doesn't exist
if not exist "Backups" mkdir "Backups"

:: Get current date and time for backup filename
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%"

echo 📅 التاريخ والوقت: %YYYY%/%MM%/%DD% - %HH%:%Min%:%Sec%
echo.

:: Check if database exists
if not exist "DocFlowSystem\DocFlowDB.db" (
    echo ❌ قاعدة البيانات غير موجودة
    echo تأكد من تشغيل النظام مرة واحدة على الأقل لإنشاء قاعدة البيانات
    pause
    exit /b 1
)

echo 🗄️ قاعدة البيانات موجودة
for %%A in ("DocFlowSystem\DocFlowDB.db") do echo 📏 حجم قاعدة البيانات: %%~zA بايت

echo.
echo 💾 جاري إنشاء النسخة الاحتياطية...

:: Copy database files
copy "DocFlowSystem\DocFlowDB.db" "Backups\DocFlowDB_backup_%datestamp%.db" >nul
if %errorlevel% equ 0 (
    echo ✅ تم إنشاء النسخة الاحتياطية بنجاح
    echo 📁 مكان الحفظ: Backups\DocFlowDB_backup_%datestamp%.db
) else (
    echo ❌ فشل في إنشاء النسخة الاحتياطية
    pause
    exit /b 1
)

:: Copy additional database files if they exist
if exist "DocFlowSystem\DocFlowDB.db-shm" (
    copy "DocFlowSystem\DocFlowDB.db-shm" "Backups\DocFlowDB_backup_%datestamp%.db-shm" >nul
    echo ✅ تم نسخ ملف .db-shm
)

if exist "DocFlowSystem\DocFlowDB.db-wal" (
    copy "DocFlowSystem\DocFlowDB.db-wal" "Backups\DocFlowDB_backup_%datestamp%.db-wal" >nul
    echo ✅ تم نسخ ملف .db-wal
)

echo.
echo 📊 إحصائيات النسخ الاحتياطية:
echo ===============================
echo 📁 مجلد النسخ الاحتياطية: Backups\
dir "Backups\DocFlowDB_backup_*.db" /b 2>nul | find /c /v "" > temp_count.txt
set /p backup_count=<temp_count.txt
del temp_count.txt
echo 📈 عدد النسخ الاحتياطية: %backup_count%

echo.
echo 💡 نصائح مهمة:
echo ==============
echo - احتفظ بالنسخ الاحتياطية في مكان آمن
echo - قم بعمل نسخة احتياطية بانتظام
echo - لاستعادة النسخة الاحتياطية، انسخ الملف إلى مجلد DocFlowSystem
echo - أعد تسمية الملف إلى DocFlowDB.db

echo.
echo ✅ تمت عملية النسخ الاحتياطي بنجاح
echo 🎉 بياناتك محفوظة بأمان!
echo.
pause
